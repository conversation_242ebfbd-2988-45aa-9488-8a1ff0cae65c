
import { PrismaClient } from '@prisma/client';
import statesData from "../data/states.json";
import {logger} from '../../src/utils/logger';
import config  from '../../src/config/config';

const prisma = new PrismaClient();

export const seedStates = async () => {


  for (const state of statesData) {
    const country = await prisma.countries.findFirst({
      where: { isoCode2: state.countryCode }, // Or isoCode2 if available
    });

    if (!country) {
    logger.error(`Country not found for state ${state.name}`);
      continue;
    }

    // Skip states without stateCode since it's required in schema
    if (!state.stateCode) {
      logger.warning(`Skipping state ${state.name} - no stateCode provided`);
      continue;
    }

    await prisma.states.create({
      data: {
        name: state.name,
        stateCode: state.stateCode,
        countryId: country.countryId,
        longitude: state.longitude,
        latitude: state.latitude,
        createdBy: config.SYSTEM_USER_ID,
        updatedBy: config.SYSTEM_USER_ID,
      },
    });
  }

  logger.info('Seeding states...');
};

export default seedStates;
