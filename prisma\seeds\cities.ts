
import { PrismaClient } from '@prisma/client';
import citiesData from "../data/cities.json";
import config from '../../src/config/config';
import {logger} from '../../src/utils/logger';

const prisma = new PrismaClient();

export const seedCities = async () => {


  for (const city of citiesData) {
    const state = await prisma.states.findFirst({
      where: { stateCode: city.stateCode || ""}, 
    });

    if (!state) {
        logger.error(`State not found for state ${city.name}`);
        continue;
    }

    await prisma.cities.create({
      data: {
        name: city.name,
        stateId: state.stateId,
        longitude:state.longitude,
        latitude:state.latitude,
        createdBy: config.SYSTEM_USER_ID,
        updatedBy: config.SYSTEM_USER_ID,
        status : (city.status as any) || "active"
      },
    });
  }

  logger.info('Seeding states...');
};

export default seedCities;
