[{"isoCode2": "AF", "isoCode3": "AFG", "numericCode": "004", "name": "Afghanistan", "phonecode": "93", "flag": "🇦🇫", "currency": "AFN", "currencySymbol": "؋", "currencyName": "Afghan afghani", "latitude": 33, "longitude": 65, "timeZones": [{"zoneName": "Asia/Kabul", "gmtOffset": 16200, "gmtOffsetName": "UTC+04:30", "abbreviation": "AFT", "tzName": "Afghanistan Time"}]}, {"isoCode2": "AX", "isoCode3": "ALA", "numericCode": "248", "name": "Aland Islands", "phonecode": "358", "flag": "🇦🇽", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 60.116667, "longitude": 19.9, "timeZones": [{"zoneName": "Europe/Mariehamn", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "AL", "isoCode3": "ALB", "numericCode": "008", "name": "Albania", "phonecode": "355", "flag": "🇦🇱", "currency": "ALL", "currencySymbol": "Lek", "currencyName": "Albanian lek", "latitude": 41, "longitude": 20, "timeZones": [{"zoneName": "Europe/Tirane", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "DZ", "isoCode3": "DZA", "numericCode": "012", "name": "Algeria", "phonecode": "213", "flag": "🇩🇿", "currency": "DZD", "currencySymbol": "دج", "currencyName": "Algerian dinar", "latitude": 28, "longitude": 3, "timeZones": [{"zoneName": "Africa/Algiers", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "AS", "isoCode3": "ASM", "numericCode": "016", "name": "American Samoa", "phonecode": "1", "flag": "🇦🇸", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": -14.33333333, "longitude": -170, "timeZones": [{"zoneName": "Pacific/Pago_Pago", "gmtOffset": -39600, "gmtOffsetName": "UTC-11:00", "abbreviation": "SST", "tzName": "Samoa Standard Time"}]}, {"isoCode2": "AD", "isoCode3": "AND", "numericCode": "020", "name": "Andorra", "phonecode": "376", "flag": "🇦🇩", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 42.5, "longitude": 1.5, "timeZones": [{"zoneName": "Europe/Andorra", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "AO", "isoCode3": "AGO", "numericCode": "024", "name": "Angola", "phonecode": "244", "flag": "🇦🇴", "currency": "AOA", "currencySymbol": "Kz", "currencyName": "Angolan kwanza", "latitude": -12.5, "longitude": 18.5, "timeZones": [{"zoneName": "Africa/Luanda", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "WAT", "tzName": "West Africa Time"}]}, {"isoCode2": "AI", "isoCode3": "AIA", "numericCode": "660", "name": "<PERSON><PERSON><PERSON>", "phonecode": "1", "flag": "🇦🇮", "currency": "XCD", "currencySymbol": "$", "currencyName": "Eastern Caribbean dollar", "latitude": 18.25, "longitude": -63.16666666, "timeZones": [{"zoneName": "America/Anguilla", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "AQ", "isoCode3": "ATA", "numericCode": "010", "name": "Antarctica", "phonecode": "672", "flag": "🇦🇶", "currency": "AAD", "currencySymbol": "$", "currencyName": "Antarctican dollar", "latitude": -74.65, "longitude": 4.48, "timeZones": [{"zoneName": "Antarctica/Casey", "gmtOffset": 39600, "gmtOffsetName": "UTC+11:00", "abbreviation": "AWST", "tzName": "Australian Western Standard Time"}, {"zoneName": "Antarctica/Davis", "gmtOffset": 25200, "gmtOffsetName": "UTC+07:00", "abbreviation": "DAVT", "tzName": "<PERSON>"}, {"zoneName": "Antarctica/DumontDUrville", "gmtOffset": 36000, "gmtOffsetName": "UTC+10:00", "abbreviation": "DDUT", "tzName": "<PERSON><PERSON> d'Urville Time"}, {"zoneName": "Antarctica/Mawson", "gmtOffset": 18000, "gmtOffsetName": "UTC+05:00", "abbreviation": "MAWT", "tzName": "Mawson Station Time"}, {"zoneName": "Antarctica/McMurdo", "gmtOffset": 46800, "gmtOffsetName": "UTC+13:00", "abbreviation": "NZDT", "tzName": "New Zealand Daylight Time"}, {"zoneName": "Antarctica/Palmer", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "CLST", "tzName": "Chile Summer Time"}, {"zoneName": "Antarctica/Rothera", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "ROTT", "tzName": "Rothera Research Station Time"}, {"zoneName": "Antarctica/Syowa", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "SYOT", "tzName": "Showa Station Time"}, {"zoneName": "Antarctica/Troll", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}, {"zoneName": "Antarctica/Vostok", "gmtOffset": 21600, "gmtOffsetName": "UTC+06:00", "abbreviation": "VOST", "tzName": "Vostok Station Time"}]}, {"isoCode2": "AG", "isoCode3": "ATG", "numericCode": "028", "name": "Antigua and Barbuda", "phonecode": "1", "flag": "🇦🇬", "currency": "XCD", "currencySymbol": "$", "currencyName": "Eastern Caribbean dollar", "latitude": 17.05, "longitude": -61.8, "timeZones": [{"zoneName": "America/Antigua", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "AR", "isoCode3": "ARG", "numericCode": "032", "name": "Argentina", "phonecode": "54", "flag": "🇦🇷", "currency": "ARS", "currencySymbol": "$", "currencyName": "Argentine peso", "latitude": -34, "longitude": -64, "timeZones": [{"zoneName": "America/Argentina/Buenos_Aires", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "ART", "tzName": "Argentina Time"}, {"zoneName": "America/Argentina/Catamarca", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "ART", "tzName": "Argentina Time"}, {"zoneName": "America/Argentina/Cordoba", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "ART", "tzName": "Argentina Time"}, {"zoneName": "America/Argentina/Jujuy", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "ART", "tzName": "Argentina Time"}, {"zoneName": "America/Argentina/La_Rioja", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "ART", "tzName": "Argentina Time"}, {"zoneName": "America/Argentina/Mendoza", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "ART", "tzName": "Argentina Time"}, {"zoneName": "America/Argentina/Rio_Gallegos", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "ART", "tzName": "Argentina Time"}, {"zoneName": "America/Argentina/Salta", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "ART", "tzName": "Argentina Time"}, {"zoneName": "America/Argentina/San_Juan", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "ART", "tzName": "Argentina Time"}, {"zoneName": "America/Argentina/San_Luis", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "ART", "tzName": "Argentina Time"}, {"zoneName": "America/Argentina/Tucuman", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "ART", "tzName": "Argentina Time"}, {"zoneName": "America/Argentina/Ushuaia", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "ART", "tzName": "Argentina Time"}]}, {"isoCode2": "AM", "isoCode3": "ARM", "numericCode": "051", "name": "Armenia", "phonecode": "374", "flag": "🇦🇲", "currency": "AMD", "currencySymbol": "֏", "currencyName": "Armenian dram", "latitude": 40, "longitude": 45, "timeZones": [{"zoneName": "Asia/Yerevan", "gmtOffset": 14400, "gmtOffsetName": "UTC+04:00", "abbreviation": "AMT", "tzName": "Armenia Time"}]}, {"isoCode2": "AW", "isoCode3": "ABW", "numericCode": "533", "name": "Aruba", "phonecode": "297", "flag": "🇦🇼", "currency": "AWG", "currencySymbol": "ƒ", "currencyName": "Aruban florin", "latitude": 12.5, "longitude": -69.96666666, "timeZones": [{"zoneName": "America/Aruba", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "AU", "isoCode3": "AUS", "numericCode": "036", "name": "Australia", "phonecode": "61", "flag": "🇦🇺", "currency": "AUD", "currencySymbol": "$", "currencyName": "Australian dollar", "latitude": -27, "longitude": 133, "timeZones": [{"zoneName": "Antarctica/Macquarie", "gmtOffset": 39600, "gmtOffsetName": "UTC+11:00", "abbreviation": "MIST", "tzName": "Macquarie Island Station Time"}, {"zoneName": "Australia/Adelaide", "gmtOffset": 37800, "gmtOffsetName": "UTC+10:30", "abbreviation": "ACDT", "tzName": "Australian Central Daylight Saving Time"}, {"zoneName": "Australia/Brisbane", "gmtOffset": 36000, "gmtOffsetName": "UTC+10:00", "abbreviation": "AEST", "tzName": "Australian Eastern Standard Time"}, {"zoneName": "Australia/Broken_Hill", "gmtOffset": 37800, "gmtOffsetName": "UTC+10:30", "abbreviation": "ACDT", "tzName": "Australian Central Daylight Saving Time"}, {"zoneName": "Australia/Currie", "gmtOffset": 39600, "gmtOffsetName": "UTC+11:00", "abbreviation": "AEDT", "tzName": "Australian Eastern Daylight Saving Time"}, {"zoneName": "Australia/Darwin", "gmtOffset": 34200, "gmtOffsetName": "UTC+09:30", "abbreviation": "ACST", "tzName": "Australian Central Standard Time"}, {"zoneName": "Australia/Eucla", "gmtOffset": 31500, "gmtOffsetName": "UTC+08:45", "abbreviation": "ACWST", "tzName": "Australian Central Western Standard Time (Unofficial)"}, {"zoneName": "Australia/Hobart", "gmtOffset": 39600, "gmtOffsetName": "UTC+11:00", "abbreviation": "AEDT", "tzName": "Australian Eastern Daylight Saving Time"}, {"zoneName": "Australia/Lindeman", "gmtOffset": 36000, "gmtOffsetName": "UTC+10:00", "abbreviation": "AEST", "tzName": "Australian Eastern Standard Time"}, {"zoneName": "Australia/Lord_Howe", "gmtOffset": 39600, "gmtOffsetName": "UTC+11:00", "abbreviation": "LHST", "tzName": "<PERSON> Summer Time"}, {"zoneName": "Australia/Melbourne", "gmtOffset": 39600, "gmtOffsetName": "UTC+11:00", "abbreviation": "AEDT", "tzName": "Australian Eastern Daylight Saving Time"}, {"zoneName": "Australia/Perth", "gmtOffset": 28800, "gmtOffsetName": "UTC+08:00", "abbreviation": "AWST", "tzName": "Australian Western Standard Time"}, {"zoneName": "Australia/Sydney", "gmtOffset": 39600, "gmtOffsetName": "UTC+11:00", "abbreviation": "AEDT", "tzName": "Australian Eastern Daylight Saving Time"}]}, {"isoCode2": "AT", "isoCode3": "AUT", "numericCode": "040", "name": "Austria", "phonecode": "43", "flag": "🇦🇹", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 47.33333333, "longitude": 13.33333333, "timeZones": [{"zoneName": "Europe/Vienna", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "AZ", "isoCode3": "AZE", "numericCode": "031", "name": "Azerbaijan", "phonecode": "994", "flag": "🇦🇿", "currency": "AZN", "currencySymbol": "m", "currencyName": "Azerbaijani manat", "latitude": 40.5, "longitude": 47.5, "timeZones": [{"zoneName": "Asia/Baku", "gmtOffset": 14400, "gmtOffsetName": "UTC+04:00", "abbreviation": "AZT", "tzName": "Azerbaijan Time"}]}, {"isoCode2": "BH", "isoCode3": "BHR", "numericCode": "048", "name": "Bahrain", "phonecode": "973", "flag": "🇧🇭", "currency": "BHD", "currencySymbol": ".د.ب", "currencyName": "<PERSON><PERSON> dinar", "latitude": 26, "longitude": 50.55, "timeZones": [{"zoneName": "Asia/Bahrain", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "AST", "tzName": "Arabia Standard Time"}]}, {"isoCode2": "BD", "isoCode3": "BGD", "numericCode": "050", "name": "Bangladesh", "phonecode": "880", "flag": "🇧🇩", "currency": "BDT", "currencySymbol": "৳", "currencyName": "Bangladeshi taka", "latitude": 24, "longitude": 90, "timeZones": [{"zoneName": "Asia/Dhaka", "gmtOffset": 21600, "gmtOffsetName": "UTC+06:00", "abbreviation": "BDT", "tzName": "Bangladesh Standard Time"}]}, {"isoCode2": "BB", "isoCode3": "BRB", "numericCode": "052", "name": "Barbados", "phonecode": "1", "flag": "🇧🇧", "currency": "BBD", "currencySymbol": "Bds$", "currencyName": "Barbadian dollar", "latitude": 13.16666666, "longitude": -59.53333333, "timeZones": [{"zoneName": "America/Barbados", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "BY", "isoCode3": "BLR", "numericCode": "112", "name": "Belarus", "phonecode": "375", "flag": "🇧🇾", "currency": "BYN", "currencySymbol": "Br", "currencyName": "Belarusian ruble", "latitude": 53, "longitude": 28, "timeZones": [{"zoneName": "Europe/Minsk", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "MSK", "tzName": "Moscow Time"}]}, {"isoCode2": "BE", "isoCode3": "BEL", "numericCode": "056", "name": "Belgium", "phonecode": "32", "flag": "🇧🇪", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 50.83333333, "longitude": 4, "timeZones": [{"zoneName": "Europe/Brussels", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "BZ", "isoCode3": "BLZ", "numericCode": "084", "name": "Belize", "phonecode": "501", "flag": "🇧🇿", "currency": "BZD", "currencySymbol": "$", "currencyName": "Belize dollar", "latitude": 17.25, "longitude": -88.75, "timeZones": [{"zoneName": "America/Belize", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America)"}]}, {"isoCode2": "BJ", "isoCode3": "BEN", "numericCode": "204", "name": "Benin", "phonecode": "229", "flag": "🇧🇯", "currency": "XOF", "currencySymbol": "CFA", "currencyName": "West African CFA franc", "latitude": 9.5, "longitude": 2.25, "timeZones": [{"zoneName": "Africa/Porto-Novo", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "WAT", "tzName": "West Africa Time"}]}, {"isoCode2": "BM", "isoCode3": "BMU", "numericCode": "060", "name": "Bermuda", "phonecode": "1", "flag": "🇧🇲", "currency": "BMD", "currencySymbol": "$", "currencyName": "Bermudian dollar", "latitude": 32.33333333, "longitude": -64.75, "timeZones": [{"zoneName": "Atlantic/Bermuda", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "BT", "isoCode3": "BTN", "numericCode": "064", "name": "Bhutan", "phonecode": "975", "flag": "🇧🇹", "currency": "BTN", "currencySymbol": "Nu.", "currencyName": "Bhutanese ngultrum", "latitude": 27.5, "longitude": 90.5, "timeZones": [{"zoneName": "Asia/Thimphu", "gmtOffset": 21600, "gmtOffsetName": "UTC+06:00", "abbreviation": "BTT", "tzName": "Bhutan Time"}]}, {"isoCode2": "BO", "isoCode3": "BOL", "numericCode": "068", "name": "Bolivia", "phonecode": "591", "flag": "🇧🇴", "currency": "BOB", "currencySymbol": "Bs.", "currencyName": "Bolivian boliviano", "latitude": -17, "longitude": -65, "timeZones": [{"zoneName": "America/La_Paz", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "BOT", "tzName": "Bolivia Time"}]}, {"isoCode2": "BQ", "isoCode3": "BES", "numericCode": "535", "name": "Bonaire, Sint Eustatius and Saba", "phonecode": "599", "flag": "🇧🇶", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": 12.15, "longitude": -68.266667, "timeZones": [{"zoneName": "America/Anguilla", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "BA", "isoCode3": "BIH", "numericCode": "070", "name": "Bosnia and Herzegovina", "phonecode": "387", "flag": "🇧🇦", "currency": "BAM", "currencySymbol": "KM", "currencyName": "Bosnia and Herzegovina convertible mark", "latitude": 44, "longitude": 18, "timeZones": [{"zoneName": "Europe/Sarajevo", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "BW", "isoCode3": "BWA", "numericCode": "072", "name": "Botswana", "phonecode": "267", "flag": "🇧🇼", "currency": "BWP", "currencySymbol": "P", "currencyName": "Botswana pula", "latitude": -22, "longitude": 24, "timeZones": [{"zoneName": "Africa/Gaborone", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "CAT", "tzName": "Central Africa Time"}]}, {"isoCode2": "BV", "isoCode3": "BVT", "numericCode": "074", "name": "Bouvet Island", "phonecode": "0055", "flag": "🇧🇻", "currency": "NOK", "currencySymbol": "ko", "currencyName": "Norwegian krone", "latitude": -54.43333333, "longitude": 3.4, "timeZones": [{"zoneName": "Europe/Oslo", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "BR", "isoCode3": "BRA", "numericCode": "076", "name": "Brazil", "phonecode": "55", "flag": "🇧🇷", "currency": "BRL", "currencySymbol": "R$", "currencyName": "Brazilian real", "latitude": -10, "longitude": -55, "timeZones": [{"zoneName": "America/Araguaina", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "BRT", "tzName": "Brasília Time"}, {"zoneName": "America/Bahia", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "BRT", "tzName": "Brasília Time"}, {"zoneName": "America/Belem", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "BRT", "tzName": "Brasília Time"}, {"zoneName": "America/Boa_Vista", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AMT", "tzName": "Amazon Time (Brazil)[3"}, {"zoneName": "America/Campo_Grande", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AMT", "tzName": "Amazon Time (Brazil)[3"}, {"zoneName": "America/Cuiaba", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "BRT", "tzName": "Brasilia Time"}, {"zoneName": "America/Eirunepe", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "ACT", "tzName": "Acre Time"}, {"zoneName": "America/Fortaleza", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "BRT", "tzName": "Brasília Time"}, {"zoneName": "America/Maceio", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "BRT", "tzName": "Brasília Time"}, {"zoneName": "America/Manaus", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AMT", "tzName": "Amazon Time (Brazil)"}, {"zoneName": "America/Noronha", "gmtOffset": -7200, "gmtOffsetName": "UTC-02:00", "abbreviation": "FNT", "tzName": "<PERSON>"}, {"zoneName": "America/Porto_Velho", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AMT", "tzName": "Amazon Time (Brazil)[3"}, {"zoneName": "America/Recife", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "BRT", "tzName": "Brasília Time"}, {"zoneName": "America/Rio_Branco", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "ACT", "tzName": "Acre Time"}, {"zoneName": "America/Santarem", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "BRT", "tzName": "Brasília Time"}, {"zoneName": "America/Sao_Paulo", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "BRT", "tzName": "Brasília Time"}]}, {"isoCode2": "IO", "isoCode3": "IOT", "numericCode": "086", "name": "British Indian Ocean Territory", "phonecode": "246", "flag": "🇮🇴", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": -6, "longitude": 71.5, "timeZones": [{"zoneName": "Indian/Chagos", "gmtOffset": 21600, "gmtOffsetName": "UTC+06:00", "abbreviation": "IOT", "tzName": "Indian Ocean Time"}]}, {"isoCode2": "BN", "isoCode3": "BRN", "numericCode": "096", "name": "Brunei", "phonecode": "673", "flag": "🇧🇳", "currency": "BND", "currencySymbol": "B$", "currencyName": "Brunei dollar", "latitude": 4.5, "longitude": 114.66666666, "timeZones": [{"zoneName": "Asia/Brunei", "gmtOffset": 28800, "gmtOffsetName": "UTC+08:00", "abbreviation": "BNT", "tzName": "Brunei Darussalam Time"}]}, {"isoCode2": "BG", "isoCode3": "BGR", "numericCode": "100", "name": "Bulgaria", "phonecode": "359", "flag": "🇧🇬", "currency": "BGN", "currencySymbol": "Л<PERSON>.", "currencyName": "Bulgarian lev", "latitude": 43, "longitude": 25, "timeZones": [{"zoneName": "Europe/Sofia", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "BF", "isoCode3": "BFA", "numericCode": "854", "name": "Burkina Faso", "phonecode": "226", "flag": "🇧🇫", "currency": "XOF", "currencySymbol": "CFA", "currencyName": "West African CFA franc", "latitude": 13, "longitude": -2, "timeZones": [{"zoneName": "Africa/Ouagadougou", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "BI", "isoCode3": "BDI", "numericCode": "108", "name": "Burundi", "phonecode": "257", "flag": "🇧🇮", "currency": "BIF", "currencySymbol": "FBu", "currencyName": "Burundian franc", "latitude": -3.5, "longitude": 30, "timeZones": [{"zoneName": "Africa/Bujumbura", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "CAT", "tzName": "Central Africa Time"}]}, {"isoCode2": "KH", "isoCode3": "KHM", "numericCode": "116", "name": "Cambodia", "phonecode": "855", "flag": "🇰🇭", "currency": "KHR", "currencySymbol": "KHR", "currencyName": "Cambodian riel", "latitude": 13, "longitude": 105, "timeZones": [{"zoneName": "Asia/Phnom_Penh", "gmtOffset": 25200, "gmtOffsetName": "UTC+07:00", "abbreviation": "ICT", "tzName": "Indochina Time"}]}, {"isoCode2": "CM", "isoCode3": "CMR", "numericCode": "120", "name": "Cameroon", "phonecode": "237", "flag": "🇨🇲", "currency": "XAF", "currencySymbol": "FCFA", "currencyName": "Central African CFA franc", "latitude": 6, "longitude": 12, "timeZones": [{"zoneName": "Africa/Douala", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "WAT", "tzName": "West Africa Time"}]}, {"isoCode2": "CA", "isoCode3": "CAN", "numericCode": "124", "name": "Canada", "phonecode": "1", "flag": "🇨🇦", "currency": "CAD", "currencySymbol": "$", "currencyName": "Canadian dollar", "latitude": 60, "longitude": -95, "timeZones": [{"zoneName": "America/Atikokan", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America)"}, {"zoneName": "America/Blanc-Sablon", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}, {"zoneName": "America/Cambridge_Bay", "gmtOffset": -25200, "gmtOffsetName": "UTC-07:00", "abbreviation": "MST", "tzName": "Mountain Standard Time (North America)"}, {"zoneName": "America/Creston", "gmtOffset": -25200, "gmtOffsetName": "UTC-07:00", "abbreviation": "MST", "tzName": "Mountain Standard Time (North America)"}, {"zoneName": "America/Dawson", "gmtOffset": -25200, "gmtOffsetName": "UTC-07:00", "abbreviation": "MST", "tzName": "Mountain Standard Time (North America)"}, {"zoneName": "America/Dawson_Creek", "gmtOffset": -25200, "gmtOffsetName": "UTC-07:00", "abbreviation": "MST", "tzName": "Mountain Standard Time (North America)"}, {"zoneName": "America/Edmonton", "gmtOffset": -25200, "gmtOffsetName": "UTC-07:00", "abbreviation": "MST", "tzName": "Mountain Standard Time (North America)"}, {"zoneName": "America/Fort_Nelson", "gmtOffset": -25200, "gmtOffsetName": "UTC-07:00", "abbreviation": "MST", "tzName": "Mountain Standard Time (North America)"}, {"zoneName": "America/Glace_Bay", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}, {"zoneName": "America/Goose_Bay", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}, {"zoneName": "America/Halifax", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}, {"zoneName": "America/Inuvik", "gmtOffset": -25200, "gmtOffsetName": "UTC-07:00", "abbreviation": "MST", "tzName": "Mountain Standard Time (North America"}, {"zoneName": "America/Iqaluit", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}, {"zoneName": "America/Moncton", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}, {"zoneName": "America/Nipigon", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}, {"zoneName": "America/Pangnirtung", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}, {"zoneName": "America/Rainy_River", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/Rankin_Inlet", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/Regina", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/Resolute", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/St_Johns", "gmtOffset": -12600, "gmtOffsetName": "UTC-03:30", "abbreviation": "NST", "tzName": "Newfoundland Standard Time"}, {"zoneName": "America/Swift_Current", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/Thunder_Bay", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}, {"zoneName": "America/Toronto", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}, {"zoneName": "America/Vancouver", "gmtOffset": -28800, "gmtOffsetName": "UTC-08:00", "abbreviation": "PST", "tzName": "Pacific Standard Time (North America"}, {"zoneName": "America/Whitehorse", "gmtOffset": -25200, "gmtOffsetName": "UTC-07:00", "abbreviation": "MST", "tzName": "Mountain Standard Time (North America"}, {"zoneName": "America/Winnipeg", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/Yellowknife", "gmtOffset": -25200, "gmtOffsetName": "UTC-07:00", "abbreviation": "MST", "tzName": "Mountain Standard Time (North America"}]}, {"isoCode2": "CV", "isoCode3": "CPV", "numericCode": "132", "name": "Cape Verde", "phonecode": "238", "flag": "🇨🇻", "currency": "CVE", "currencySymbol": "$", "currencyName": "Cape Verdean escudo", "latitude": 16, "longitude": -24, "timeZones": [{"zoneName": "Atlantic/Cape_Verde", "gmtOffset": -3600, "gmtOffsetName": "UTC-01:00", "abbreviation": "CVT", "tzName": "Cape Verde Time"}]}, {"isoCode2": "KY", "isoCode3": "CYM", "numericCode": "136", "name": "Cayman Islands", "phonecode": "1", "flag": "🇰🇾", "currency": "KYD", "currencySymbol": "$", "currencyName": "Cayman Islands dollar", "latitude": 19.5, "longitude": -80.5, "timeZones": [{"zoneName": "America/Cayman", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}]}, {"isoCode2": "CF", "isoCode3": "CAF", "numericCode": "140", "name": "Central African Republic", "phonecode": "236", "flag": "🇨🇫", "currency": "XAF", "currencySymbol": "FCFA", "currencyName": "Central African CFA franc", "latitude": 7, "longitude": 21, "timeZones": [{"zoneName": "Africa/Bangui", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "WAT", "tzName": "West Africa Time"}]}, {"isoCode2": "TD", "isoCode3": "TCD", "numericCode": "148", "name": "Chad", "phonecode": "235", "flag": "🇹🇩", "currency": "XAF", "currencySymbol": "FCFA", "currencyName": "Central African CFA franc", "latitude": 15, "longitude": 19, "timeZones": [{"zoneName": "Africa/Ndjamena", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "WAT", "tzName": "West Africa Time"}]}, {"isoCode2": "CL", "isoCode3": "CHL", "numericCode": "152", "name": "Chile", "phonecode": "56", "flag": "🇨🇱", "currency": "CLP", "currencySymbol": "$", "currencyName": "Chilean peso", "latitude": -30, "longitude": -71, "timeZones": [{"zoneName": "America/Punta_Arenas", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "CLST", "tzName": "Chile Summer Time"}, {"zoneName": "America/Santiago", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "CLST", "tzName": "Chile Summer Time"}, {"zoneName": "Pacific/Easter", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EASST", "tzName": "Easter Island Summer Time"}]}, {"isoCode2": "CN", "isoCode3": "CHN", "numericCode": "156", "name": "China", "phonecode": "86", "flag": "🇨🇳", "currency": "CNY", "currencySymbol": "¥", "currencyName": "Chinese yuan", "latitude": 35, "longitude": 105, "timeZones": [{"zoneName": "Asia/Shanghai", "gmtOffset": 28800, "gmtOffsetName": "UTC+08:00", "abbreviation": "CST", "tzName": "China Standard Time"}, {"zoneName": "Asia/Urumqi", "gmtOffset": 21600, "gmtOffsetName": "UTC+06:00", "abbreviation": "XJT", "tzName": "China Standard Time"}]}, {"isoCode2": "CX", "isoCode3": "CXR", "numericCode": "162", "name": "Christmas Island", "phonecode": "61", "flag": "🇨🇽", "currency": "AUD", "currencySymbol": "$", "currencyName": "Australian dollar", "latitude": -10.5, "longitude": 105.66666666, "timeZones": [{"zoneName": "Indian/Christmas", "gmtOffset": 25200, "gmtOffsetName": "UTC+07:00", "abbreviation": "CXT", "tzName": "Christmas Island Time"}]}, {"isoCode2": "CC", "isoCode3": "CCK", "numericCode": "166", "name": "Cocos (Keeling) Islands", "phonecode": "61", "flag": "🇨🇨", "currency": "AUD", "currencySymbol": "$", "currencyName": "Australian dollar", "latitude": -12.5, "longitude": 96.83333333, "timeZones": [{"zoneName": "Indian/Cocos", "gmtOffset": 23400, "gmtOffsetName": "UTC+06:30", "abbreviation": "CCT", "tzName": "Cocos Islands Time"}]}, {"isoCode2": "CO", "isoCode3": "COL", "numericCode": "170", "name": "Colombia", "phonecode": "57", "flag": "🇨🇴", "currency": "COP", "currencySymbol": "$", "currencyName": "Colombian peso", "latitude": 4, "longitude": -72, "timeZones": [{"zoneName": "America/Bogota", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "COT", "tzName": "Colombia Time"}]}, {"isoCode2": "KM", "isoCode3": "COM", "numericCode": "174", "name": "Comoros", "phonecode": "269", "flag": "🇰🇲", "currency": "KMF", "currencySymbol": "CF", "currencyName": "Comorian franc", "latitude": -12.16666666, "longitude": 44.25, "timeZones": [{"zoneName": "Indian/Comoro", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "EAT", "tzName": "East Africa Time"}]}, {"isoCode2": "CG", "isoCode3": "COG", "numericCode": "178", "name": "Congo", "phonecode": "242", "flag": "🇨🇬", "currency": "XAF", "currencySymbol": "CDF", "currencyName": "Congolese Franc", "latitude": -1, "longitude": 15, "timeZones": [{"zoneName": "Africa/Brazzaville", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "WAT", "tzName": "West Africa Time"}]}, {"isoCode2": "CK", "isoCode3": "COK", "numericCode": "184", "name": "Cook Islands", "phonecode": "682", "flag": "🇨🇰", "currency": "NZD", "currencySymbol": "$", "currencyName": "New Zealand dollar", "latitude": -21.23333333, "longitude": -159.76666666, "timeZones": [{"zoneName": "Pacific/Rarotonga", "gmtOffset": -36000, "gmtOffsetName": "UTC-10:00", "abbreviation": "CKT", "tzName": "Cook Island Time"}]}, {"isoCode2": "CR", "isoCode3": "CRI", "numericCode": "188", "name": "Costa Rica", "phonecode": "506", "flag": "🇨🇷", "currency": "CRC", "currencySymbol": "₡", "currencyName": "Costa Rican colón", "latitude": 10, "longitude": -84, "timeZones": [{"zoneName": "America/Costa_Rica", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}]}, {"isoCode2": "CI", "isoCode3": "CIV", "numericCode": "384", "name": "Cote D'Ivoire (Ivory Coast)", "phonecode": "225", "flag": "🇨🇮", "currency": "XOF", "currencySymbol": "CFA", "currencyName": "West African CFA franc", "latitude": 8, "longitude": -5, "timeZones": [{"zoneName": "Africa/Abidjan", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "HR", "isoCode3": "HRV", "numericCode": "191", "name": "Croatia", "phonecode": "385", "flag": "🇭🇷", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 45.16666666, "longitude": 15.5, "timeZones": [{"zoneName": "Europe/Zagreb", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "CU", "isoCode3": "CUB", "numericCode": "192", "name": "Cuba", "phonecode": "53", "flag": "🇨🇺", "currency": "CUP", "currencySymbol": "$", "currencyName": "Cuban peso", "latitude": 21.5, "longitude": -80, "timeZones": [{"zoneName": "America/Havana", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "CST", "tzName": "Cuba Standard Time"}]}, {"isoCode2": "CW", "isoCode3": "CUW", "numericCode": "531", "name": "Curaçao", "phonecode": "599", "flag": "🇨🇼", "currency": "ANG", "currencySymbol": "ƒ", "currencyName": "Netherlands Antillean guilder", "latitude": 12.116667, "longitude": -68.933333, "timeZones": [{"zoneName": "America/Curacao", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "CY", "isoCode3": "CYP", "numericCode": "196", "name": "Cyprus", "phonecode": "357", "flag": "🇨🇾", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 35, "longitude": 33, "timeZones": [{"zoneName": "Asia/Famagusta", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}, {"zoneName": "Asia/Nicosia", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "CZ", "isoCode3": "CZE", "numericCode": "203", "name": "Czech Republic", "phonecode": "420", "flag": "🇨🇿", "currency": "CZK", "currencySymbol": "Kč", "currencyName": "Czech koruna", "latitude": 49.75, "longitude": 15.5, "timeZones": [{"zoneName": "Europe/Prague", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "CD", "isoCode3": "COD", "numericCode": "180", "name": "Democratic Republic of the Congo", "phonecode": "243", "flag": "🇨🇩", "currency": "CDF", "currencySymbol": "FC", "currencyName": "Congolese Franc", "latitude": 0, "longitude": 25, "timeZones": [{"zoneName": "Africa/Kinshasa", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "WAT", "tzName": "West Africa Time"}, {"zoneName": "Africa/Lubumbashi", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "CAT", "tzName": "Central Africa Time"}]}, {"isoCode2": "DK", "isoCode3": "DNK", "numericCode": "208", "name": "Denmark", "phonecode": "45", "flag": "🇩🇰", "currency": "DKK", "currencySymbol": "<PERSON>r.", "currencyName": "Danish krone", "latitude": 56, "longitude": 10, "timeZones": [{"zoneName": "Europe/Copenhagen", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "DJ", "isoCode3": "DJI", "numericCode": "262", "name": "Djibouti", "phonecode": "253", "flag": "🇩🇯", "currency": "DJF", "currencySymbol": "Fdj", "currencyName": "Djiboutian franc", "latitude": 11.5, "longitude": 43, "timeZones": [{"zoneName": "Africa/Djibouti", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "EAT", "tzName": "East Africa Time"}]}, {"isoCode2": "DM", "isoCode3": "DMA", "numericCode": "212", "name": "Dominica", "phonecode": "1", "flag": "🇩🇲", "currency": "XCD", "currencySymbol": "$", "currencyName": "Eastern Caribbean dollar", "latitude": 15.41666666, "longitude": -61.33333333, "timeZones": [{"zoneName": "America/Dominica", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "DO", "isoCode3": "DOM", "numericCode": "214", "name": "Dominican Republic", "phonecode": "1", "flag": "🇩🇴", "currency": "DOP", "currencySymbol": "$", "currencyName": "Dominican peso", "latitude": 19, "longitude": -70.66666666, "timeZones": [{"zoneName": "America/Santo_Domingo", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "EC", "isoCode3": "ECU", "numericCode": "218", "name": "Ecuador", "phonecode": "593", "flag": "🇪🇨", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": -2, "longitude": -77.5, "timeZones": [{"zoneName": "America/Guayaquil", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "ECT", "tzName": "Ecuador Time"}, {"zoneName": "Pacific/Galapagos", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "GALT", "tzName": "Galápagos Time"}]}, {"isoCode2": "EG", "isoCode3": "EGY", "numericCode": "818", "name": "Egypt", "phonecode": "20", "flag": "🇪🇬", "currency": "EGP", "currencySymbol": "ج.م", "currencyName": "Egyptian pound", "latitude": 27, "longitude": 30, "timeZones": [{"zoneName": "Africa/Cairo", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "SV", "isoCode3": "SLV", "numericCode": "222", "name": "El Salvador", "phonecode": "503", "flag": "🇸🇻", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": 13.83333333, "longitude": -88.91666666, "timeZones": [{"zoneName": "America/El_Salvador", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}]}, {"isoCode2": "GQ", "isoCode3": "GNQ", "numericCode": "226", "name": "Equatorial Guinea", "phonecode": "240", "flag": "🇬🇶", "currency": "XAF", "currencySymbol": "FCFA", "currencyName": "Central African CFA franc", "latitude": 2, "longitude": 10, "timeZones": [{"zoneName": "Africa/Malabo", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "WAT", "tzName": "West Africa Time"}]}, {"isoCode2": "ER", "isoCode3": "ERI", "numericCode": "232", "name": "Eritrea", "phonecode": "291", "flag": "🇪🇷", "currency": "ERN", "currencySymbol": "Nfk", "currencyName": "Eritrean nakfa", "latitude": 15, "longitude": 39, "timeZones": [{"zoneName": "Africa/Asmara", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "EAT", "tzName": "East Africa Time"}]}, {"isoCode2": "EE", "isoCode3": "EST", "numericCode": "233", "name": "Estonia", "phonecode": "372", "flag": "🇪🇪", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 59, "longitude": 26, "timeZones": [{"zoneName": "Europe/Tallinn", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "SZ", "isoCode3": "SWZ", "numericCode": "748", "name": "<PERSON><PERSON><PERSON><PERSON>", "phonecode": "268", "flag": "🇸🇿", "currency": "SZL", "currencySymbol": "E", "currencyName": "<PERSON><PERSON><PERSON>", "latitude": -26.5, "longitude": 31.5, "timeZones": [{"zoneName": "Africa/Mbabane", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "SAST", "tzName": "South African Standard Time"}]}, {"isoCode2": "ET", "isoCode3": "ETH", "numericCode": "231", "name": "Ethiopia", "phonecode": "251", "flag": "🇪🇹", "currency": "ETB", "currencySymbol": "Nkf", "currencyName": "Ethiopian birr", "latitude": 8, "longitude": 38, "timeZones": [{"zoneName": "Africa/Addis_Ababa", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "EAT", "tzName": "East Africa Time"}]}, {"isoCode2": "FK", "isoCode3": "FLK", "numericCode": "238", "name": "Falkland Islands", "phonecode": "500", "flag": "🇫🇰", "currency": "FKP", "currencySymbol": "£", "currencyName": "Falkland Islands pound", "latitude": -51.75, "longitude": -59, "timeZones": [{"zoneName": "Atlantic/Stanley", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "FKST", "tzName": "Falkland Islands Summer Time"}]}, {"isoCode2": "FO", "isoCode3": "FRO", "numericCode": "234", "name": "Faroe Islands", "phonecode": "298", "flag": "🇫🇴", "currency": "DKK", "currencySymbol": "<PERSON>r.", "currencyName": "Danish krone", "latitude": 62, "longitude": -7, "timeZones": [{"zoneName": "Atlantic/Faroe", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "WET", "tzName": "Western European Time"}]}, {"isoCode2": "FJ", "isoCode3": "FJI", "numericCode": "242", "name": "Fiji Islands", "phonecode": "679", "flag": "🇫🇯", "currency": "FJD", "currencySymbol": "FJ$", "currencyName": "Fijian dollar", "latitude": -18, "longitude": 175, "timeZones": [{"zoneName": "Pacific/Fiji", "gmtOffset": 43200, "gmtOffsetName": "UTC+12:00", "abbreviation": "FJT", "tzName": "Fiji Time"}]}, {"isoCode2": "FI", "isoCode3": "FIN", "numericCode": "246", "name": "Finland", "phonecode": "358", "flag": "🇫🇮", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 64, "longitude": 26, "timeZones": [{"zoneName": "Europe/Helsinki", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "FR", "isoCode3": "FRA", "numericCode": "250", "name": "France", "phonecode": "33", "flag": "🇫🇷", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 46, "longitude": 2, "timeZones": [{"zoneName": "Europe/Paris", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "GF", "isoCode3": "GUF", "numericCode": "254", "name": "French Guiana", "phonecode": "594", "flag": "🇬🇫", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 4, "longitude": -53, "timeZones": [{"zoneName": "America/Cayenne", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "GFT", "tzName": "French Guiana Time"}]}, {"isoCode2": "PF", "isoCode3": "PYF", "numericCode": "258", "name": "French Polynesia", "phonecode": "689", "flag": "🇵🇫", "currency": "XPF", "currencySymbol": "₣", "currencyName": "CFP franc", "latitude": -15, "longitude": -140, "timeZones": [{"zoneName": "Pacific/Gambier", "gmtOffset": -32400, "gmtOffsetName": "UTC-09:00", "abbreviation": "GAMT", "tzName": "Gambier Islands Time"}, {"zoneName": "Pacific/Marquesas", "gmtOffset": -34200, "gmtOffsetName": "UTC-09:30", "abbreviation": "MART", "tzName": "Marquesas Islands Time"}, {"zoneName": "Pacific/Tahiti", "gmtOffset": -36000, "gmtOffsetName": "UTC-10:00", "abbreviation": "TAHT", "tzName": "Tahiti Time"}]}, {"isoCode2": "TF", "isoCode3": "ATF", "numericCode": "260", "name": "French Southern Territories", "phonecode": "262", "flag": "🇹🇫", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": -49.25, "longitude": 69.167, "timeZones": [{"zoneName": "Indian/Kerguelen", "gmtOffset": 18000, "gmtOffsetName": "UTC+05:00", "abbreviation": "TFT", "tzName": "French Southern and Antarctic Time"}]}, {"isoCode2": "GA", "isoCode3": "GAB", "numericCode": "266", "name": "Gabon", "phonecode": "241", "flag": "🇬🇦", "currency": "XAF", "currencySymbol": "FCFA", "currencyName": "Central African CFA franc", "latitude": -1, "longitude": 11.75, "timeZones": [{"zoneName": "Africa/Libreville", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "WAT", "tzName": "West Africa Time"}]}, {"isoCode2": "GE", "isoCode3": "GEO", "numericCode": "268", "name": "Georgia", "phonecode": "995", "flag": "🇬🇪", "currency": "GEL", "currencySymbol": "ლ", "currencyName": "Georgian lari", "latitude": 42, "longitude": 43.5, "timeZones": [{"zoneName": "Asia/Tbilisi", "gmtOffset": 14400, "gmtOffsetName": "UTC+04:00", "abbreviation": "GET", "tzName": "Georgia Standard Time"}]}, {"isoCode2": "DE", "isoCode3": "DEU", "numericCode": "276", "name": "Germany", "phonecode": "49", "flag": "🇩🇪", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 51, "longitude": 9, "timeZones": [{"zoneName": "Europe/Berlin", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}, {"zoneName": "Europe/Busingen", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "GH", "isoCode3": "GHA", "numericCode": "288", "name": "Ghana", "phonecode": "233", "flag": "🇬🇭", "currency": "GHS", "currencySymbol": "GH₵", "currencyName": "Ghanaian cedi", "latitude": 8, "longitude": -2, "timeZones": [{"zoneName": "Africa/Accra", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "GI", "isoCode3": "GIB", "numericCode": "292", "name": "Gibraltar", "phonecode": "350", "flag": "🇬🇮", "currency": "GIP", "currencySymbol": "£", "currencyName": "Gibraltar pound", "latitude": 36.13333333, "longitude": -5.35, "timeZones": [{"zoneName": "Europe/Gibraltar", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "GR", "isoCode3": "GRC", "numericCode": "300", "name": "Greece", "phonecode": "30", "flag": "🇬🇷", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 39, "longitude": 22, "timeZones": [{"zoneName": "Europe/Athens", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "GL", "isoCode3": "GRL", "numericCode": "304", "name": "Greenland", "phonecode": "299", "flag": "🇬🇱", "currency": "DKK", "currencySymbol": "<PERSON>r.", "currencyName": "Danish krone", "latitude": 72, "longitude": -40, "timeZones": [{"zoneName": "America/Danmarkshavn", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}, {"zoneName": "America/Nuuk", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "WGT", "tzName": "West Greenland Time"}, {"zoneName": "America/Scoresbysund", "gmtOffset": -3600, "gmtOffsetName": "UTC-01:00", "abbreviation": "EGT", "tzName": "Eastern Greenland Time"}, {"zoneName": "America/Thule", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "GD", "isoCode3": "GRD", "numericCode": "308", "name": "Grenada", "phonecode": "1", "flag": "🇬🇩", "currency": "XCD", "currencySymbol": "$", "currencyName": "Eastern Caribbean dollar", "latitude": 12.11666666, "longitude": -61.66666666, "timeZones": [{"zoneName": "America/Grenada", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "GP", "isoCode3": "GLP", "numericCode": "312", "name": "Guadeloupe", "phonecode": "590", "flag": "🇬🇵", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 16.25, "longitude": -61.583333, "timeZones": [{"zoneName": "America/Guadeloupe", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "GU", "isoCode3": "GUM", "numericCode": "316", "name": "Guam", "phonecode": "1", "flag": "🇬🇺", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": 13.46666666, "longitude": 144.78333333, "timeZones": [{"zoneName": "Pacific/Guam", "gmtOffset": 36000, "gmtOffsetName": "UTC+10:00", "abbreviation": "CHST", "tzName": "Chamorro Standard Time"}]}, {"isoCode2": "GT", "isoCode3": "GTM", "numericCode": "320", "name": "Guatemala", "phonecode": "502", "flag": "🇬🇹", "currency": "GTQ", "currencySymbol": "Q", "currencyName": "Guatemalan quetzal", "latitude": 15.5, "longitude": -90.25, "timeZones": [{"zoneName": "America/Guatemala", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}]}, {"isoCode2": "GG", "isoCode3": "GGY", "numericCode": "831", "name": "Guernsey", "phonecode": "44", "flag": "🇬🇬", "currency": "GBP", "currencySymbol": "£", "currencyName": "British pound", "latitude": 49.46666666, "longitude": -2.58333333, "timeZones": [{"zoneName": "Europe/Guernsey", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "GN", "isoCode3": "GIN", "numericCode": "324", "name": "Guinea", "phonecode": "224", "flag": "🇬🇳", "currency": "GNF", "currencySymbol": "FG", "currencyName": "Guinean franc", "latitude": 11, "longitude": -10, "timeZones": [{"zoneName": "Africa/Conakry", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "GW", "isoCode3": "GNB", "numericCode": "624", "name": "Guinea-Bissau", "phonecode": "245", "flag": "🇬🇼", "currency": "XOF", "currencySymbol": "CFA", "currencyName": "West African CFA franc", "latitude": 12, "longitude": -15, "timeZones": [{"zoneName": "Africa/Bissau", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "GY", "isoCode3": "GUY", "numericCode": "328", "name": "Guyana", "phonecode": "592", "flag": "🇬🇾", "currency": "GYD", "currencySymbol": "$", "currencyName": "Guyanese dollar", "latitude": 5, "longitude": -59, "timeZones": [{"zoneName": "America/Guyana", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "GYT", "tzName": "Guyana Time"}]}, {"isoCode2": "HT", "isoCode3": "HTI", "numericCode": "332", "name": "Haiti", "phonecode": "509", "flag": "🇭🇹", "currency": "HTG", "currencySymbol": "G", "currencyName": "Haitian gourde", "latitude": 19, "longitude": -72.41666666, "timeZones": [{"zoneName": "America/Port-au-Prince", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}]}, {"isoCode2": "HM", "isoCode3": "HMD", "numericCode": "334", "name": "Heard Island and McDonald Islands", "phonecode": "672", "flag": "🇭🇲", "currency": "AUD", "currencySymbol": "$", "currencyName": "Australian dollar", "latitude": -53.1, "longitude": 72.51666666, "timeZones": [{"zoneName": "Indian/Kerguelen", "gmtOffset": 18000, "gmtOffsetName": "UTC+05:00", "abbreviation": "TFT", "tzName": "French Southern and Antarctic Time"}]}, {"isoCode2": "HN", "isoCode3": "HND", "numericCode": "340", "name": "Honduras", "phonecode": "504", "flag": "🇭🇳", "currency": "HNL", "currencySymbol": "L", "currencyName": "<PERSON><PERSON><PERSON>", "latitude": 15, "longitude": -86.5, "timeZones": [{"zoneName": "America/Tegucigalpa", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}]}, {"isoCode2": "HK", "isoCode3": "HKG", "numericCode": "344", "name": "Hong Kong S.A.R.", "phonecode": "852", "flag": "🇭🇰", "currency": "HKD", "currencySymbol": "$", "currencyName": "Hong Kong dollar", "latitude": 22.25, "longitude": 114.16666666, "timeZones": [{"zoneName": "Asia/Hong_Kong", "gmtOffset": 28800, "gmtOffsetName": "UTC+08:00", "abbreviation": "HKT", "tzName": "Hong Kong Time"}]}, {"isoCode2": "HU", "isoCode3": "HUN", "numericCode": "348", "name": "Hungary", "phonecode": "36", "flag": "🇭🇺", "currency": "HUF", "currencySymbol": "Ft", "currencyName": "Hungarian forint", "latitude": 47, "longitude": 20, "timeZones": [{"zoneName": "Europe/Budapest", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "IS", "isoCode3": "ISL", "numericCode": "352", "name": "Iceland", "phonecode": "354", "flag": "🇮🇸", "currency": "ISK", "currencySymbol": "ko", "currencyName": "Icelandic króna", "latitude": 65, "longitude": -18, "timeZones": [{"zoneName": "Atlantic/Reykjavik", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "IN", "isoCode3": "IND", "numericCode": "356", "name": "India", "phonecode": "91", "flag": "🇮🇳", "currency": "INR", "currencySymbol": "₹", "currencyName": "Indian rupee", "latitude": 20, "longitude": 77, "timeZones": [{"zoneName": "Asia/Kolkata", "gmtOffset": 19800, "gmtOffsetName": "UTC+05:30", "abbreviation": "IST", "tzName": "Indian Standard Time"}]}, {"isoCode2": "ID", "isoCode3": "IDN", "numericCode": "360", "name": "Indonesia", "phonecode": "62", "flag": "🇮🇩", "currency": "IDR", "currencySymbol": "Rp", "currencyName": "Indonesian rupiah", "latitude": -5, "longitude": 120, "timeZones": [{"zoneName": "Asia/Jakarta", "gmtOffset": 25200, "gmtOffsetName": "UTC+07:00", "abbreviation": "WIB", "tzName": "Western Indonesian Time"}, {"zoneName": "Asia/Jayapura", "gmtOffset": 32400, "gmtOffsetName": "UTC+09:00", "abbreviation": "WIT", "tzName": "Eastern Indonesian Time"}, {"zoneName": "Asia/Makassar", "gmtOffset": 28800, "gmtOffsetName": "UTC+08:00", "abbreviation": "WITA", "tzName": "Central Indonesia Time"}, {"zoneName": "Asia/Pontianak", "gmtOffset": 25200, "gmtOffsetName": "UTC+07:00", "abbreviation": "WIB", "tzName": "Western Indonesian Time"}]}, {"isoCode2": "IR", "isoCode3": "IRN", "numericCode": "364", "name": "Iran", "phonecode": "98", "flag": "🇮🇷", "currency": "IRR", "currencySymbol": "﷼", "currencyName": "Iranian rial", "latitude": 32, "longitude": 53, "timeZones": [{"zoneName": "Asia/Tehran", "gmtOffset": 12600, "gmtOffsetName": "UTC+03:30", "abbreviation": "IRDT", "tzName": "Iran Daylight Time"}]}, {"isoCode2": "IQ", "isoCode3": "IRQ", "numericCode": "368", "name": "Iraq", "phonecode": "964", "flag": "🇮🇶", "currency": "IQD", "currencySymbol": "د.ع", "currencyName": "Iraqi dinar", "latitude": 33, "longitude": 44, "timeZones": [{"zoneName": "Asia/Baghdad", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "AST", "tzName": "Arabia Standard Time"}]}, {"isoCode2": "IE", "isoCode3": "IRL", "numericCode": "372", "name": "Ireland", "phonecode": "353", "flag": "🇮🇪", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 53, "longitude": -8, "timeZones": [{"zoneName": "Europe/Dublin", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "IL", "isoCode3": "ISR", "numericCode": "376", "name": "Israel", "phonecode": "972", "flag": "🇮🇱", "currency": "ILS", "currencySymbol": "₪", "currencyName": "Israeli new shekel", "latitude": 31.5, "longitude": 34.75, "timeZones": [{"zoneName": "Asia/Jerusalem", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "IST", "tzName": "Israel Standard Time"}]}, {"isoCode2": "IT", "isoCode3": "ITA", "numericCode": "380", "name": "Italy", "phonecode": "39", "flag": "🇮🇹", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 42.83333333, "longitude": 12.83333333, "timeZones": [{"zoneName": "Europe/Rome", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "JM", "isoCode3": "JAM", "numericCode": "388", "name": "Jamaica", "phonecode": "1", "flag": "🇯🇲", "currency": "JMD", "currencySymbol": "J$", "currencyName": "Jamaican dollar", "latitude": 18.25, "longitude": -77.5, "timeZones": [{"zoneName": "America/Jamaica", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}]}, {"isoCode2": "JP", "isoCode3": "JPN", "numericCode": "392", "name": "Japan", "phonecode": "81", "flag": "🇯🇵", "currency": "JPY", "currencySymbol": "¥", "currencyName": "Japanese yen", "latitude": 36, "longitude": 138, "timeZones": [{"zoneName": "Asia/Tokyo", "gmtOffset": 32400, "gmtOffsetName": "UTC+09:00", "abbreviation": "JST", "tzName": "Japan Standard Time"}]}, {"isoCode2": "JE", "isoCode3": "JEY", "numericCode": "832", "name": "Jersey", "phonecode": "44", "flag": "🇯🇪", "currency": "GBP", "currencySymbol": "£", "currencyName": "British pound", "latitude": 49.25, "longitude": -2.16666666, "timeZones": [{"zoneName": "Europe/Jersey", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "JO", "isoCode3": "JOR", "numericCode": "400", "name": "Jordan", "phonecode": "962", "flag": "🇯🇴", "currency": "JOD", "currencySymbol": "ا.د", "currencyName": "Jordanian dinar", "latitude": 31, "longitude": 36, "timeZones": [{"zoneName": "Asia/Amman", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "KZ", "isoCode3": "KAZ", "numericCode": "398", "name": "Kazakhstan", "phonecode": "7", "flag": "🇰🇿", "currency": "KZT", "currencySymbol": "лв", "currencyName": "Kazakhstani tenge", "latitude": 48, "longitude": 68, "timeZones": [{"zoneName": "Asia/Almaty", "gmtOffset": 21600, "gmtOffsetName": "UTC+06:00", "abbreviation": "ALMT", "tzName": "Alma-Ata Time[1"}, {"zoneName": "Asia/Aqtau", "gmtOffset": 18000, "gmtOffsetName": "UTC+05:00", "abbreviation": "AQTT", "tzName": "Aqtobe Time"}, {"zoneName": "Asia/Aqtobe", "gmtOffset": 18000, "gmtOffsetName": "UTC+05:00", "abbreviation": "AQTT", "tzName": "Aqtobe Time"}, {"zoneName": "Asia/Atyrau", "gmtOffset": 18000, "gmtOffsetName": "UTC+05:00", "abbreviation": "MSD+1", "tzName": "Moscow Daylight Time+1"}, {"zoneName": "Asia/Oral", "gmtOffset": 18000, "gmtOffsetName": "UTC+05:00", "abbreviation": "ORAT", "tzName": "Oral Time"}, {"zoneName": "Asia/Qostanay", "gmtOffset": 21600, "gmtOffsetName": "UTC+06:00", "abbreviation": "QYZST", "tzName": "Qyzylorda Summer Time"}, {"zoneName": "Asia/Qyzylorda", "gmtOffset": 18000, "gmtOffsetName": "UTC+05:00", "abbreviation": "QYZT", "tzName": "Qyzylorda Summer Time"}]}, {"isoCode2": "KE", "isoCode3": "KEN", "numericCode": "404", "name": "Kenya", "phonecode": "254", "flag": "🇰🇪", "currency": "KES", "currencySymbol": "KSh", "currencyName": "Kenyan shilling", "latitude": 1, "longitude": 38, "timeZones": [{"zoneName": "Africa/Nairobi", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "EAT", "tzName": "East Africa Time"}]}, {"isoCode2": "KI", "isoCode3": "KIR", "numericCode": "296", "name": "Kiribati", "phonecode": "686", "flag": "🇰🇮", "currency": "AUD", "currencySymbol": "$", "currencyName": "Australian dollar", "latitude": 1.41666666, "longitude": 173, "timeZones": [{"zoneName": "Pacific/Enderbury", "gmtOffset": 46800, "gmtOffsetName": "UTC+13:00", "abbreviation": "PHOT", "tzName": "Phoenix Island Time"}, {"zoneName": "Pacific/Kiritimati", "gmtOffset": 50400, "gmtOffsetName": "UTC+14:00", "abbreviation": "LINT", "tzName": "Line Islands Time"}, {"zoneName": "Pacific/Tarawa", "gmtOffset": 43200, "gmtOffsetName": "UTC+12:00", "abbreviation": "GILT", "tzName": "Gilbert Island Time"}]}, {"isoCode2": "XK", "isoCode3": "XKX", "numericCode": "926", "name": "Kosovo", "phonecode": "383", "flag": "🇽🇰", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 42.5612909, "longitude": 20.3403035, "timeZones": [{"zoneName": "Europe/Belgrade", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "KW", "isoCode3": "KWT", "numericCode": "414", "name": "Kuwait", "phonecode": "965", "flag": "🇰🇼", "currency": "KWD", "currencySymbol": "ك.د", "currencyName": "Kuwaiti dinar", "latitude": 29.5, "longitude": 45.75, "timeZones": [{"zoneName": "Asia/Kuwait", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "AST", "tzName": "Arabia Standard Time"}]}, {"isoCode2": "KG", "isoCode3": "KGZ", "numericCode": "417", "name": "Kyrgyzstan", "phonecode": "996", "flag": "🇰🇬", "currency": "KGS", "currencySymbol": "лв", "currencyName": "Kyrgyzstani som", "latitude": 41, "longitude": 75, "timeZones": [{"zoneName": "Asia/Bishkek", "gmtOffset": 21600, "gmtOffsetName": "UTC+06:00", "abbreviation": "KGT", "tzName": "Kyrgyzstan Time"}]}, {"isoCode2": "LA", "isoCode3": "LAO", "numericCode": "418", "name": "Laos", "phonecode": "856", "flag": "🇱🇦", "currency": "LAK", "currencySymbol": "₭", "currencyName": "Lao kip", "latitude": 18, "longitude": 105, "timeZones": [{"zoneName": "Asia/Vientiane", "gmtOffset": 25200, "gmtOffsetName": "UTC+07:00", "abbreviation": "ICT", "tzName": "Indochina Time"}]}, {"isoCode2": "LV", "isoCode3": "LVA", "numericCode": "428", "name": "Latvia", "phonecode": "371", "flag": "🇱🇻", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 57, "longitude": 25, "timeZones": [{"zoneName": "Europe/Riga", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "LB", "isoCode3": "LBN", "numericCode": "422", "name": "Lebanon", "phonecode": "961", "flag": "🇱🇧", "currency": "LBP", "currencySymbol": "£", "currencyName": "Lebanese pound", "latitude": 33.83333333, "longitude": 35.83333333, "timeZones": [{"zoneName": "Asia/Beirut", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "LS", "isoCode3": "LSO", "numericCode": "426", "name": "Lesotho", "phonecode": "266", "flag": "🇱🇸", "currency": "LSL", "currencySymbol": "L", "currencyName": "Lesotho loti", "latitude": -29.5, "longitude": 28.5, "timeZones": [{"zoneName": "Africa/Maseru", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "SAST", "tzName": "South African Standard Time"}]}, {"isoCode2": "LR", "isoCode3": "LBR", "numericCode": "430", "name": "Liberia", "phonecode": "231", "flag": "🇱🇷", "currency": "LRD", "currencySymbol": "$", "currencyName": "Liberian dollar", "latitude": 6.5, "longitude": -9.5, "timeZones": [{"zoneName": "Africa/Monrovia", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "LY", "isoCode3": "LBY", "numericCode": "434", "name": "Libya", "phonecode": "218", "flag": "🇱🇾", "currency": "LYD", "currencySymbol": "د.ل", "currencyName": "Libyan dinar", "latitude": 25, "longitude": 17, "timeZones": [{"zoneName": "Africa/Tripoli", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "LI", "isoCode3": "LIE", "numericCode": "438", "name": "Liechtenstein", "phonecode": "423", "flag": "🇱🇮", "currency": "CHF", "currencySymbol": "CHf", "currencyName": "Swiss franc", "latitude": 47.26666666, "longitude": 9.53333333, "timeZones": [{"zoneName": "Europe/Vaduz", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "LT", "isoCode3": "LTU", "numericCode": "440", "name": "Lithuania", "phonecode": "370", "flag": "🇱🇹", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 56, "longitude": 24, "timeZones": [{"zoneName": "Europe/Vilnius", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "LU", "isoCode3": "LUX", "numericCode": "442", "name": "Luxembourg", "phonecode": "352", "flag": "🇱🇺", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 49.75, "longitude": 6.16666666, "timeZones": [{"zoneName": "Europe/Luxembourg", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "MO", "isoCode3": "MAC", "numericCode": "446", "name": "Macau S.A.R.", "phonecode": "853", "flag": "🇲🇴", "currency": "MOP", "currencySymbol": "$", "currencyName": "Macanese pataca", "latitude": 22.16666666, "longitude": 113.55, "timeZones": [{"zoneName": "Asia/Macau", "gmtOffset": 28800, "gmtOffsetName": "UTC+08:00", "abbreviation": "CST", "tzName": "China Standard Time"}]}, {"isoCode2": "MG", "isoCode3": "MDG", "numericCode": "450", "name": "Madagascar", "phonecode": "261", "flag": "🇲🇬", "currency": "MGA", "currencySymbol": "Ar", "currencyName": "Malagasy ariary", "latitude": -20, "longitude": 47, "timeZones": [{"zoneName": "Indian/Antananarivo", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "EAT", "tzName": "East Africa Time"}]}, {"isoCode2": "MW", "isoCode3": "MWI", "numericCode": "454", "name": "Malawi", "phonecode": "265", "flag": "🇲🇼", "currency": "MWK", "currencySymbol": "MK", "currencyName": "Malawian kwacha", "latitude": -13.5, "longitude": 34, "timeZones": [{"zoneName": "Africa/Blantyre", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "CAT", "tzName": "Central Africa Time"}]}, {"isoCode2": "MY", "isoCode3": "MYS", "numericCode": "458", "name": "Malaysia", "phonecode": "60", "flag": "🇲🇾", "currency": "MYR", "currencySymbol": "RM", "currencyName": "Malaysian ringgit", "latitude": 2.5, "longitude": 112.5, "timeZones": [{"zoneName": "Asia/Kuala_Lumpur", "gmtOffset": 28800, "gmtOffsetName": "UTC+08:00", "abbreviation": "MYT", "tzName": "Malaysia Time"}, {"zoneName": "Asia/Kuching", "gmtOffset": 28800, "gmtOffsetName": "UTC+08:00", "abbreviation": "MYT", "tzName": "Malaysia Time"}]}, {"isoCode2": "MV", "isoCode3": "MDV", "numericCode": "462", "name": "Maldives", "phonecode": "960", "flag": "🇲🇻", "currency": "MVR", "currencySymbol": "Rf", "currencyName": "Maldivian rufiyaa", "latitude": 3.25, "longitude": 73, "timeZones": [{"zoneName": "Indian/Maldives", "gmtOffset": 18000, "gmtOffsetName": "UTC+05:00", "abbreviation": "MVT", "tzName": "Maldives Time"}]}, {"isoCode2": "ML", "isoCode3": "MLI", "numericCode": "466", "name": "Mali", "phonecode": "223", "flag": "🇲🇱", "currency": "XOF", "currencySymbol": "CFA", "currencyName": "West African CFA franc", "latitude": 17, "longitude": -4, "timeZones": [{"zoneName": "Africa/Bamako", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "MT", "isoCode3": "MLT", "numericCode": "470", "name": "Malta", "phonecode": "356", "flag": "🇲🇹", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 35.83333333, "longitude": 14.58333333, "timeZones": [{"zoneName": "Europe/Malta", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "IM", "isoCode3": "IMN", "numericCode": "833", "name": "Man (Isle of)", "phonecode": "44", "flag": "🇮🇲", "currency": "GBP", "currencySymbol": "£", "currencyName": "British pound", "latitude": 54.25, "longitude": -4.5, "timeZones": [{"zoneName": "Europe/Isle_of_Man", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "MH", "isoCode3": "MHL", "numericCode": "584", "name": "Marshall Islands", "phonecode": "692", "flag": "🇲🇭", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": 9, "longitude": 168, "timeZones": [{"zoneName": "Pacific/Kwajalein", "gmtOffset": 43200, "gmtOffsetName": "UTC+12:00", "abbreviation": "MHT", "tzName": "Marshall Islands Time"}, {"zoneName": "Pacific/Majuro", "gmtOffset": 43200, "gmtOffsetName": "UTC+12:00", "abbreviation": "MHT", "tzName": "Marshall Islands Time"}]}, {"isoCode2": "MQ", "isoCode3": "MTQ", "numericCode": "474", "name": "Martinique", "phonecode": "596", "flag": "🇲🇶", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 14.666667, "longitude": -61, "timeZones": [{"zoneName": "America/Martinique", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "MR", "isoCode3": "MRT", "numericCode": "478", "name": "Mauritania", "phonecode": "222", "flag": "🇲🇷", "currency": "MRU", "currencySymbol": "UM", "currencyName": "Mauritanian ouguiya", "latitude": 20, "longitude": -12, "timeZones": [{"zoneName": "Africa/Nouakchott", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "MU", "isoCode3": "MUS", "numericCode": "480", "name": "Mauritius", "phonecode": "230", "flag": "🇲🇺", "currency": "MUR", "currencySymbol": "₨", "currencyName": "Mauritian rupee", "latitude": -20.28333333, "longitude": 57.55, "timeZones": [{"zoneName": "Indian/Mauritius", "gmtOffset": 14400, "gmtOffsetName": "UTC+04:00", "abbreviation": "MUT", "tzName": "Mauritius Time"}]}, {"isoCode2": "YT", "isoCode3": "MYT", "numericCode": "175", "name": "Mayotte", "phonecode": "262", "flag": "🇾🇹", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": -12.83333333, "longitude": 45.16666666, "timeZones": [{"zoneName": "Indian/Mayotte", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "EAT", "tzName": "East Africa Time"}]}, {"isoCode2": "MX", "isoCode3": "MEX", "numericCode": "484", "name": "Mexico", "phonecode": "52", "flag": "🇲🇽", "currency": "MXN", "currencySymbol": "$", "currencyName": "Mexican peso", "latitude": 23, "longitude": -102, "timeZones": [{"zoneName": "America/Bahia_Banderas", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/Cancun", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}, {"zoneName": "America/Chihuahua", "gmtOffset": -25200, "gmtOffsetName": "UTC-07:00", "abbreviation": "MST", "tzName": "Mountain Standard Time (North America"}, {"zoneName": "America/Hermosillo", "gmtOffset": -25200, "gmtOffsetName": "UTC-07:00", "abbreviation": "MST", "tzName": "Mountain Standard Time (North America"}, {"zoneName": "America/Matamoros", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/Mazatlan", "gmtOffset": -25200, "gmtOffsetName": "UTC-07:00", "abbreviation": "MST", "tzName": "Mountain Standard Time (North America"}, {"zoneName": "America/Merida", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/Mexico_City", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/Monterrey", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/Ojinaga", "gmtOffset": -25200, "gmtOffsetName": "UTC-07:00", "abbreviation": "MST", "tzName": "Mountain Standard Time (North America"}, {"zoneName": "America/Tijuana", "gmtOffset": -28800, "gmtOffsetName": "UTC-08:00", "abbreviation": "PST", "tzName": "Pacific Standard Time (North America"}]}, {"isoCode2": "FM", "isoCode3": "FSM", "numericCode": "583", "name": "Micronesia", "phonecode": "691", "flag": "🇫🇲", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": 6.91666666, "longitude": 158.25, "timeZones": [{"zoneName": "Pacific/Chuuk", "gmtOffset": 36000, "gmtOffsetName": "UTC+10:00", "abbreviation": "CHUT", "tzName": "Chuuk Time"}, {"zoneName": "Pacific/Kosrae", "gmtOffset": 39600, "gmtOffsetName": "UTC+11:00", "abbreviation": "KOST", "tzName": "Kosrae Time"}, {"zoneName": "Pacific/Pohnpei", "gmtOffset": 39600, "gmtOffsetName": "UTC+11:00", "abbreviation": "PONT", "tzName": "Pohnpei Standard Time"}]}, {"isoCode2": "MD", "isoCode3": "MDA", "numericCode": "498", "name": "Moldova", "phonecode": "373", "flag": "🇲🇩", "currency": "MDL", "currencySymbol": "L", "currencyName": "Moldovan leu", "latitude": 47, "longitude": 29, "timeZones": [{"zoneName": "Europe/Chisinau", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "MC", "isoCode3": "MCO", "numericCode": "492", "name": "Monaco", "phonecode": "377", "flag": "🇲🇨", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 43.73333333, "longitude": 7.4, "timeZones": [{"zoneName": "Europe/Monaco", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "MN", "isoCode3": "MNG", "numericCode": "496", "name": "Mongolia", "phonecode": "976", "flag": "🇲🇳", "currency": "MNT", "currencySymbol": "₮", "currencyName": "Mongolian tögrög", "latitude": 46, "longitude": 105, "timeZones": [{"zoneName": "Asia/Choibalsan", "gmtOffset": 28800, "gmtOffsetName": "UTC+08:00", "abbreviation": "CHOT", "tzName": "Choibalsan Standard Time"}, {"zoneName": "Asia/Hovd", "gmtOffset": 25200, "gmtOffsetName": "UTC+07:00", "abbreviation": "HOVT", "tzName": "Hovd Time"}, {"zoneName": "Asia/Ulaanbaatar", "gmtOffset": 28800, "gmtOffsetName": "UTC+08:00", "abbreviation": "ULAT", "tzName": "Ulaanbaatar Standard Time"}]}, {"isoCode2": "ME", "isoCode3": "MNE", "numericCode": "499", "name": "Montenegro", "phonecode": "382", "flag": "🇲🇪", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 42.5, "longitude": 19.3, "timeZones": [{"zoneName": "Europe/Podgorica", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "MS", "isoCode3": "MSR", "numericCode": "500", "name": "Montserrat", "phonecode": "1", "flag": "🇲🇸", "currency": "XCD", "currencySymbol": "$", "currencyName": "Eastern Caribbean dollar", "latitude": 16.75, "longitude": -62.2, "timeZones": [{"zoneName": "America/Montserrat", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "MA", "isoCode3": "MAR", "numericCode": "504", "name": "Morocco", "phonecode": "212", "flag": "🇲🇦", "currency": "MAD", "currencySymbol": "DH", "currencyName": "Moroccan dirham", "latitude": 32, "longitude": -5, "timeZones": [{"zoneName": "Africa/Casablanca", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "WEST", "tzName": "Western European Summer Time"}]}, {"isoCode2": "MZ", "isoCode3": "MOZ", "numericCode": "508", "name": "Mozambique", "phonecode": "258", "flag": "🇲🇿", "currency": "MZN", "currencySymbol": "MT", "currencyName": "Mozambican metical", "latitude": -18.25, "longitude": 35, "timeZones": [{"zoneName": "Africa/Maputo", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "CAT", "tzName": "Central Africa Time"}]}, {"isoCode2": "MM", "isoCode3": "MMR", "numericCode": "104", "name": "Myanmar", "phonecode": "95", "flag": "🇲🇲", "currency": "MMK", "currencySymbol": "K", "currencyName": "Burmese kyat", "latitude": 22, "longitude": 98, "timeZones": [{"zoneName": "Asia/Yangon", "gmtOffset": 23400, "gmtOffsetName": "UTC+06:30", "abbreviation": "MMT", "tzName": "Myanmar Standard Time"}]}, {"isoCode2": "NA", "isoCode3": "NAM", "numericCode": "516", "name": "Namibia", "phonecode": "264", "flag": "🇳🇦", "currency": "NAD", "currencySymbol": "$", "currencyName": "Namibian dollar", "latitude": -22, "longitude": 17, "timeZones": [{"zoneName": "Africa/Windhoek", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "WAST", "tzName": "West Africa Summer Time"}]}, {"isoCode2": "NR", "isoCode3": "NRU", "numericCode": "520", "name": "Nauru", "phonecode": "674", "flag": "🇳🇷", "currency": "AUD", "currencySymbol": "$", "currencyName": "Australian dollar", "latitude": -0.53333333, "longitude": 166.91666666, "timeZones": [{"zoneName": "Pacific/Nauru", "gmtOffset": 43200, "gmtOffsetName": "UTC+12:00", "abbreviation": "NRT", "tzName": "Nauru Time"}]}, {"isoCode2": "NP", "isoCode3": "NPL", "numericCode": "524", "name": "Nepal", "phonecode": "977", "flag": "🇳🇵", "currency": "NPR", "currencySymbol": "₨", "currencyName": "Nepalese rupee", "latitude": 28, "longitude": 84, "timeZones": [{"zoneName": "Asia/Kathmandu", "gmtOffset": 20700, "gmtOffsetName": "UTC+05:45", "abbreviation": "NPT", "tzName": "Nepal Time"}]}, {"isoCode2": "NL", "isoCode3": "NLD", "numericCode": "528", "name": "Netherlands", "phonecode": "31", "flag": "🇳🇱", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 52.5, "longitude": 5.75, "timeZones": [{"zoneName": "Europe/Amsterdam", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "NC", "isoCode3": "NCL", "numericCode": "540", "name": "New Caledonia", "phonecode": "687", "flag": "🇳🇨", "currency": "XPF", "currencySymbol": "₣", "currencyName": "CFP franc", "latitude": -21.5, "longitude": 165.5, "timeZones": [{"zoneName": "Pacific/Noumea", "gmtOffset": 39600, "gmtOffsetName": "UTC+11:00", "abbreviation": "NCT", "tzName": "New Caledonia Time"}]}, {"isoCode2": "NZ", "isoCode3": "NZL", "numericCode": "554", "name": "New Zealand", "phonecode": "64", "flag": "🇳🇿", "currency": "NZD", "currencySymbol": "$", "currencyName": "New Zealand dollar", "latitude": -41, "longitude": 174, "timeZones": [{"zoneName": "Pacific/Auckland", "gmtOffset": 46800, "gmtOffsetName": "UTC+13:00", "abbreviation": "NZDT", "tzName": "New Zealand Daylight Time"}, {"zoneName": "Pacific/Chatham", "gmtOffset": 49500, "gmtOffsetName": "UTC+13:45", "abbreviation": "CHAST", "tzName": "Chatham Standard Time"}]}, {"isoCode2": "NI", "isoCode3": "NIC", "numericCode": "558", "name": "Nicaragua", "phonecode": "505", "flag": "🇳🇮", "currency": "NIO", "currencySymbol": "C$", "currencyName": "Nicaraguan córdoba", "latitude": 13, "longitude": -85, "timeZones": [{"zoneName": "America/Managua", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}]}, {"isoCode2": "NE", "isoCode3": "NER", "numericCode": "562", "name": "Niger", "phonecode": "227", "flag": "🇳🇪", "currency": "XOF", "currencySymbol": "CFA", "currencyName": "West African CFA franc", "latitude": 16, "longitude": 8, "timeZones": [{"zoneName": "Africa/Niamey", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "WAT", "tzName": "West Africa Time"}]}, {"isoCode2": "NG", "isoCode3": "NGA", "numericCode": "566", "name": "Nigeria", "phonecode": "234", "flag": "🇳🇬", "currency": "NGN", "currencySymbol": "₦", "currencyName": "Nigerian naira", "latitude": 10, "longitude": 8, "timeZones": [{"zoneName": "Africa/Lagos", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "WAT", "tzName": "West Africa Time"}]}, {"isoCode2": "NU", "isoCode3": "NIU", "numericCode": "570", "name": "Niue", "phonecode": "683", "flag": "🇳🇺", "currency": "NZD", "currencySymbol": "$", "currencyName": "New Zealand dollar", "latitude": -19.03333333, "longitude": -169.86666666, "timeZones": [{"zoneName": "Pacific/Niue", "gmtOffset": -39600, "gmtOffsetName": "UTC-11:00", "abbreviation": "NUT", "tzName": "Niue Time"}]}, {"isoCode2": "NF", "isoCode3": "NFK", "numericCode": "574", "name": "Norfolk Island", "phonecode": "672", "flag": "🇳🇫", "currency": "AUD", "currencySymbol": "$", "currencyName": "Australian dollar", "latitude": -29.03333333, "longitude": 167.95, "timeZones": [{"zoneName": "Pacific/Norfolk", "gmtOffset": 43200, "gmtOffsetName": "UTC+12:00", "abbreviation": "NFT", "tzName": "Norfolk Time"}]}, {"isoCode2": "KP", "isoCode3": "PRK", "numericCode": "408", "name": "North Korea", "phonecode": "850", "flag": "🇰🇵", "currency": "KPW", "currencySymbol": "₩", "currencyName": "North Korean Won", "latitude": 40, "longitude": 127, "timeZones": [{"zoneName": "Asia/Pyongyang", "gmtOffset": 32400, "gmtOffsetName": "UTC+09:00", "abbreviation": "KST", "tzName": "Korea Standard Time"}]}, {"isoCode2": "MK", "isoCode3": "MKD", "numericCode": "807", "name": "North Macedonia", "phonecode": "389", "flag": "🇲🇰", "currency": "MKD", "currencySymbol": "ден", "currencyName": "<PERSON><PERSON>", "latitude": 41.83333333, "longitude": 22, "timeZones": [{"zoneName": "Europe/Skopje", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "MP", "isoCode3": "MNP", "numericCode": "580", "name": "Northern Mariana Islands", "phonecode": "1", "flag": "🇲🇵", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": 15.2, "longitude": 145.75, "timeZones": [{"zoneName": "Pacific/Saipan", "gmtOffset": 36000, "gmtOffsetName": "UTC+10:00", "abbreviation": "ChST", "tzName": "Chamorro Standard Time"}]}, {"isoCode2": "NO", "isoCode3": "NOR", "numericCode": "578", "name": "Norway", "phonecode": "47", "flag": "🇳🇴", "currency": "NOK", "currencySymbol": "ko", "currencyName": "Norwegian krone", "latitude": 62, "longitude": 10, "timeZones": [{"zoneName": "Europe/Oslo", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "OM", "isoCode3": "OMN", "numericCode": "512", "name": "Oman", "phonecode": "968", "flag": "🇴🇲", "currency": "OMR", "currencySymbol": ".ع.ر", "currencyName": "<PERSON><PERSON> rial", "latitude": 21, "longitude": 57, "timeZones": [{"zoneName": "Asia/Muscat", "gmtOffset": 14400, "gmtOffsetName": "UTC+04:00", "abbreviation": "GST", "tzName": "Gulf Standard Time"}]}, {"isoCode2": "PK", "isoCode3": "PAK", "numericCode": "586", "name": "Pakistan", "phonecode": "92", "flag": "🇵🇰", "currency": "PKR", "currencySymbol": "₨", "currencyName": "Pakistani rupee", "latitude": 30, "longitude": 70, "timeZones": [{"zoneName": "Asia/Karachi", "gmtOffset": 18000, "gmtOffsetName": "UTC+05:00", "abbreviation": "PKT", "tzName": "Pakistan Standard Time"}]}, {"isoCode2": "PW", "isoCode3": "PLW", "numericCode": "585", "name": "<PERSON><PERSON>", "phonecode": "680", "flag": "🇵🇼", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": 7.5, "longitude": 134.5, "timeZones": [{"zoneName": "Pacific/Palau", "gmtOffset": 32400, "gmtOffsetName": "UTC+09:00", "abbreviation": "PWT", "tzName": "<PERSON><PERSON>"}]}, {"isoCode2": "PS", "isoCode3": "PSE", "numericCode": "275", "name": "Palestinian Territory Occupied", "phonecode": "970", "flag": "🇵🇸", "currency": "ILS", "currencySymbol": "₪", "currencyName": "Israeli new shekel", "latitude": 31.9, "longitude": 35.2, "timeZones": [{"zoneName": "Asia/Gaza", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}, {"zoneName": "Asia/Hebron", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "PA", "isoCode3": "PAN", "numericCode": "591", "name": "Panama", "phonecode": "507", "flag": "🇵🇦", "currency": "PAB", "currencySymbol": "B/.", "currencyName": "Panamanian balboa", "latitude": 9, "longitude": -80, "timeZones": [{"zoneName": "America/Panama", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}]}, {"isoCode2": "PG", "isoCode3": "PNG", "numericCode": "598", "name": "Papua New Guinea", "phonecode": "675", "flag": "🇵🇬", "currency": "PGK", "currencySymbol": "K", "currencyName": "Papua New Guinean kina", "latitude": -6, "longitude": 147, "timeZones": [{"zoneName": "Pacific/Bougainville", "gmtOffset": 39600, "gmtOffsetName": "UTC+11:00", "abbreviation": "BST", "tzName": "Bougainville Standard Time[6"}, {"zoneName": "Pacific/Port_Moresby", "gmtOffset": 36000, "gmtOffsetName": "UTC+10:00", "abbreviation": "PGT", "tzName": "Papua New Guinea Time"}]}, {"isoCode2": "PY", "isoCode3": "PRY", "numericCode": "600", "name": "Paraguay", "phonecode": "595", "flag": "🇵🇾", "currency": "PYG", "currencySymbol": "₲", "currencyName": "Paraguayan guarani", "latitude": -23, "longitude": -58, "timeZones": [{"zoneName": "America/Asuncion", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "PYST", "tzName": "Paraguay Summer Time"}]}, {"isoCode2": "PE", "isoCode3": "PER", "numericCode": "604", "name": "Peru", "phonecode": "51", "flag": "🇵🇪", "currency": "PEN", "currencySymbol": "S/.", "currencyName": "Peruvian sol", "latitude": -10, "longitude": -76, "timeZones": [{"zoneName": "America/Lima", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "PET", "tzName": "Peru Time"}]}, {"isoCode2": "PH", "isoCode3": "PHL", "numericCode": "608", "name": "Philippines", "phonecode": "63", "flag": "🇵🇭", "currency": "PHP", "currencySymbol": "₱", "currencyName": "Philippine peso", "latitude": 13, "longitude": 122, "timeZones": [{"zoneName": "Asia/Manila", "gmtOffset": 28800, "gmtOffsetName": "UTC+08:00", "abbreviation": "PHT", "tzName": "Philippine Time"}]}, {"isoCode2": "PN", "isoCode3": "PCN", "numericCode": "612", "name": "Pitcairn Island", "phonecode": "870", "flag": "🇵🇳", "currency": "NZD", "currencySymbol": "$", "currencyName": "New Zealand dollar", "latitude": -25.06666666, "longitude": -130.1, "timeZones": [{"zoneName": "Pacific/Pitcairn", "gmtOffset": -28800, "gmtOffsetName": "UTC-08:00", "abbreviation": "PST", "tzName": "Pacific Standard Time (North America"}]}, {"isoCode2": "PL", "isoCode3": "POL", "numericCode": "616", "name": "Poland", "phonecode": "48", "flag": "🇵🇱", "currency": "PLN", "currencySymbol": "zł", "currencyName": "Polish złoty", "latitude": 52, "longitude": 20, "timeZones": [{"zoneName": "Europe/Warsaw", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "PT", "isoCode3": "PRT", "numericCode": "620", "name": "Portugal", "phonecode": "351", "flag": "🇵🇹", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 39.5, "longitude": -8, "timeZones": [{"zoneName": "Atlantic/Azores", "gmtOffset": -3600, "gmtOffsetName": "UTC-01:00", "abbreviation": "AZOT", "tzName": "Azores Standard Time"}, {"zoneName": "Atlantic/Madeira", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "WET", "tzName": "Western European Time"}, {"zoneName": "Europe/Lisbon", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "WET", "tzName": "Western European Time"}]}, {"isoCode2": "PR", "isoCode3": "PRI", "numericCode": "630", "name": "Puerto Rico", "phonecode": "1", "flag": "🇵🇷", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": 18.25, "longitude": -66.5, "timeZones": [{"zoneName": "America/Puerto_Rico", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "QA", "isoCode3": "QAT", "numericCode": "634", "name": "Qatar", "phonecode": "974", "flag": "🇶🇦", "currency": "QAR", "currencySymbol": "ق.ر", "currencyName": "Qatari riyal", "latitude": 25.5, "longitude": 51.25, "timeZones": [{"zoneName": "Asia/Qatar", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "AST", "tzName": "Arabia Standard Time"}]}, {"isoCode2": "RE", "isoCode3": "REU", "numericCode": "638", "name": "Reunion", "phonecode": "262", "flag": "🇷🇪", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": -21.15, "longitude": 55.5, "timeZones": [{"zoneName": "Indian/Reunion", "gmtOffset": 14400, "gmtOffsetName": "UTC+04:00", "abbreviation": "RET", "tzName": "Réunion Time"}]}, {"isoCode2": "RO", "isoCode3": "ROU", "numericCode": "642", "name": "Romania", "phonecode": "40", "flag": "🇷🇴", "currency": "RON", "currencySymbol": "lei", "currencyName": "Romanian leu", "latitude": 46, "longitude": 25, "timeZones": [{"zoneName": "Europe/Bucharest", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "RU", "isoCode3": "RUS", "numericCode": "643", "name": "Russia", "phonecode": "7", "flag": "🇷🇺", "currency": "RUB", "currencySymbol": "₽", "currencyName": "Russian ruble", "latitude": 60, "longitude": 100, "timeZones": [{"zoneName": "Asia/Anadyr", "gmtOffset": 43200, "gmtOffsetName": "UTC+12:00", "abbreviation": "ANAT", "tzName": "<PERSON><PERSON><PERSON>[4"}, {"zoneName": "Asia/Barnaul", "gmtOffset": 25200, "gmtOffsetName": "UTC+07:00", "abbreviation": "KRAT", "tzName": "Krasnoyarsk Time"}, {"zoneName": "Asia/Chita", "gmtOffset": 32400, "gmtOffsetName": "UTC+09:00", "abbreviation": "YAKT", "tzName": "Yakutsk Time"}, {"zoneName": "Asia/Irkutsk", "gmtOffset": 28800, "gmtOffsetName": "UTC+08:00", "abbreviation": "IRKT", "tzName": "Irkutsk Time"}, {"zoneName": "Asia/Kamchatka", "gmtOffset": 43200, "gmtOffsetName": "UTC+12:00", "abbreviation": "PETT", "tzName": "Kamchatka Time"}, {"zoneName": "Asia/Khandyga", "gmtOffset": 32400, "gmtOffsetName": "UTC+09:00", "abbreviation": "YAKT", "tzName": "Yakutsk Time"}, {"zoneName": "Asia/Krasnoyarsk", "gmtOffset": 25200, "gmtOffsetName": "UTC+07:00", "abbreviation": "KRAT", "tzName": "Krasnoyarsk Time"}, {"zoneName": "Asia/Magadan", "gmtOffset": 39600, "gmtOffsetName": "UTC+11:00", "abbreviation": "MAGT", "tzName": "Magadan Time"}, {"zoneName": "Asia/Novokuznetsk", "gmtOffset": 25200, "gmtOffsetName": "UTC+07:00", "abbreviation": "KRAT", "tzName": "Krasnoyarsk Time"}, {"zoneName": "Asia/Novosibirsk", "gmtOffset": 25200, "gmtOffsetName": "UTC+07:00", "abbreviation": "NOVT", "tzName": "Novosibirsk Time"}, {"zoneName": "Asia/Omsk", "gmtOffset": 21600, "gmtOffsetName": "UTC+06:00", "abbreviation": "OMST", "tzName": "Omsk Time"}, {"zoneName": "Asia/Sakhalin", "gmtOffset": 39600, "gmtOffsetName": "UTC+11:00", "abbreviation": "SAKT", "tzName": "Sakhalin Island Time"}, {"zoneName": "Asia/Srednekolymsk", "gmtOffset": 39600, "gmtOffsetName": "UTC+11:00", "abbreviation": "SRET", "tzName": "Srednekolymsk Time"}, {"zoneName": "Asia/Tomsk", "gmtOffset": 25200, "gmtOffsetName": "UTC+07:00", "abbreviation": "MSD+3", "tzName": "Moscow Daylight Time+3"}, {"zoneName": "Asia/Ust-Nera", "gmtOffset": 36000, "gmtOffsetName": "UTC+10:00", "abbreviation": "VLAT", "tzName": "Vladivostok Time"}, {"zoneName": "Asia/Vladivostok", "gmtOffset": 36000, "gmtOffsetName": "UTC+10:00", "abbreviation": "VLAT", "tzName": "Vladivostok Time"}, {"zoneName": "Asia/Yakutsk", "gmtOffset": 32400, "gmtOffsetName": "UTC+09:00", "abbreviation": "YAKT", "tzName": "Yakutsk Time"}, {"zoneName": "Asia/Yekaterinburg", "gmtOffset": 18000, "gmtOffsetName": "UTC+05:00", "abbreviation": "YEKT", "tzName": "Yekaterinburg Time"}, {"zoneName": "Europe/Astrakhan", "gmtOffset": 14400, "gmtOffsetName": "UTC+04:00", "abbreviation": "SAMT", "tzName": "Samara Time"}, {"zoneName": "Europe/Kaliningrad", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}, {"zoneName": "Europe/Kirov", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "MSK", "tzName": "Moscow Time"}, {"zoneName": "Europe/Moscow", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "MSK", "tzName": "Moscow Time"}, {"zoneName": "Europe/Samara", "gmtOffset": 14400, "gmtOffsetName": "UTC+04:00", "abbreviation": "SAMT", "tzName": "Samara Time"}, {"zoneName": "Europe/Saratov", "gmtOffset": 14400, "gmtOffsetName": "UTC+04:00", "abbreviation": "MSD", "tzName": "Moscow Daylight Time+4"}, {"zoneName": "Europe/Ulyanovsk", "gmtOffset": 14400, "gmtOffsetName": "UTC+04:00", "abbreviation": "SAMT", "tzName": "Samara Time"}, {"zoneName": "Europe/Volgograd", "gmtOffset": 14400, "gmtOffsetName": "UTC+04:00", "abbreviation": "MSK", "tzName": "Moscow Standard Time"}]}, {"isoCode2": "RW", "isoCode3": "RWA", "numericCode": "646", "name": "Rwanda", "phonecode": "250", "flag": "🇷🇼", "currency": "RWF", "currencySymbol": "FRw", "currencyName": "Rwandan franc", "latitude": -2, "longitude": 30, "timeZones": [{"zoneName": "Africa/Kigali", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "CAT", "tzName": "Central Africa Time"}]}, {"isoCode2": "SH", "isoCode3": "SHN", "numericCode": "654", "name": "Saint Helena", "phonecode": "290", "flag": "🇸🇭", "currency": "SHP", "currencySymbol": "£", "currencyName": "Saint Helena pound", "latitude": -15.95, "longitude": -5.7, "timeZones": [{"zoneName": "Atlantic/St_Helena", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "KN", "isoCode3": "KNA", "numericCode": "659", "name": "Saint Kitts and Nevis", "phonecode": "1", "flag": "🇰🇳", "currency": "XCD", "currencySymbol": "$", "currencyName": "Eastern Caribbean dollar", "latitude": 17.33333333, "longitude": -62.75, "timeZones": [{"zoneName": "America/St_Kitts", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "LC", "isoCode3": "LCA", "numericCode": "662", "name": "Saint Lucia", "phonecode": "1", "flag": "🇱🇨", "currency": "XCD", "currencySymbol": "$", "currencyName": "Eastern Caribbean dollar", "latitude": 13.88333333, "longitude": -60.96666666, "timeZones": [{"zoneName": "America/St_Lucia", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "PM", "isoCode3": "SPM", "numericCode": "666", "name": "Saint Pierre and Miquelon", "phonecode": "508", "flag": "🇵🇲", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 46.83333333, "longitude": -56.33333333, "timeZones": [{"zoneName": "America/Miquelon", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "PMDT", "tzName": "Pierre & Miquelon Daylight Time"}]}, {"isoCode2": "VC", "isoCode3": "VCT", "numericCode": "670", "name": "Saint Vincent and the Grenadines", "phonecode": "1", "flag": "🇻🇨", "currency": "XCD", "currencySymbol": "$", "currencyName": "Eastern Caribbean dollar", "latitude": 13.25, "longitude": -61.2, "timeZones": [{"zoneName": "America/St_Vincent", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "BL", "isoCode3": "BLM", "numericCode": "652", "name": "Saint-<PERSON><PERSON><PERSON><PERSON>", "phonecode": "590", "flag": "🇧🇱", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 18.5, "longitude": -63.41666666, "timeZones": [{"zoneName": "America/St_Barthelemy", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "MF", "isoCode3": "MAF", "numericCode": "663", "name": "<PERSON><PERSON><PERSON> (French part)", "phonecode": "590", "flag": "🇲🇫", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 18.08333333, "longitude": -63.95, "timeZones": [{"zoneName": "America/Marigot", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "WS", "isoCode3": "WSM", "numericCode": "882", "name": "Samoa", "phonecode": "685", "flag": "🇼🇸", "currency": "WST", "currencySymbol": "SAT", "currencyName": "Samoan tālā", "latitude": -13.58333333, "longitude": -172.33333333, "timeZones": [{"zoneName": "Pacific/Apia", "gmtOffset": 50400, "gmtOffsetName": "UTC+14:00", "abbreviation": "WST", "tzName": "West Samoa Time"}]}, {"isoCode2": "SM", "isoCode3": "SMR", "numericCode": "674", "name": "San Marino", "phonecode": "378", "flag": "🇸🇲", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 43.76666666, "longitude": 12.41666666, "timeZones": [{"zoneName": "Europe/San_Marino", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "ST", "isoCode3": "STP", "numericCode": "678", "name": "Sao Tome and Principe", "phonecode": "239", "flag": "🇸🇹", "currency": "STN", "currencySymbol": "Db", "currencyName": "Dobra", "latitude": 1, "longitude": 7, "timeZones": [{"zoneName": "Africa/Sao_Tome", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "SA", "isoCode3": "SAU", "numericCode": "682", "name": "Saudi Arabia", "phonecode": "966", "flag": "🇸🇦", "currency": "SAR", "currencySymbol": "﷼", "currencyName": "Saudi riyal", "latitude": 25, "longitude": 45, "timeZones": [{"zoneName": "Asia/Riyadh", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "AST", "tzName": "Arabia Standard Time"}]}, {"isoCode2": "SN", "isoCode3": "SEN", "numericCode": "686", "name": "Senegal", "phonecode": "221", "flag": "🇸🇳", "currency": "XOF", "currencySymbol": "CFA", "currencyName": "West African CFA franc", "latitude": 14, "longitude": -14, "timeZones": [{"zoneName": "Africa/Dakar", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "RS", "isoCode3": "SRB", "numericCode": "688", "name": "Serbia", "phonecode": "381", "flag": "🇷🇸", "currency": "RSD", "currencySymbol": "din", "currencyName": "Serbian dinar", "latitude": 44, "longitude": 21, "timeZones": [{"zoneName": "Europe/Belgrade", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "SC", "isoCode3": "SYC", "numericCode": "690", "name": "Seychelles", "phonecode": "248", "flag": "🇸🇨", "currency": "SCR", "currencySymbol": "SRe", "currencyName": "Seychellois rupee", "latitude": -4.58333333, "longitude": 55.66666666, "timeZones": [{"zoneName": "Indian/Mahe", "gmtOffset": 14400, "gmtOffsetName": "UTC+04:00", "abbreviation": "SCT", "tzName": "Seychelles Time"}]}, {"isoCode2": "SL", "isoCode3": "SLE", "numericCode": "694", "name": "Sierra Leone", "phonecode": "232", "flag": "🇸🇱", "currency": "SLL", "currencySymbol": "Le", "currencyName": "Sierra Leonean leone", "latitude": 8.5, "longitude": -11.5, "timeZones": [{"zoneName": "Africa/Freetown", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "SG", "isoCode3": "SGP", "numericCode": "702", "name": "Singapore", "phonecode": "65", "flag": "🇸🇬", "currency": "SGD", "currencySymbol": "$", "currencyName": "Singapore dollar", "latitude": 1.36666666, "longitude": 103.8, "timeZones": [{"zoneName": "Asia/Singapore", "gmtOffset": 28800, "gmtOffsetName": "UTC+08:00", "abbreviation": "SGT", "tzName": "Singapore Time"}]}, {"isoCode2": "SX", "isoCode3": "SXM", "numericCode": "534", "name": "<PERSON><PERSON> Ma<PERSON>n (Dutch part)", "phonecode": "1721", "flag": "🇸🇽", "currency": "ANG", "currencySymbol": "ƒ", "currencyName": "Netherlands Antillean guilder", "latitude": 18.033333, "longitude": -63.05, "timeZones": [{"zoneName": "America/Anguilla", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "SK", "isoCode3": "SVK", "numericCode": "703", "name": "Slovakia", "phonecode": "421", "flag": "🇸🇰", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 48.66666666, "longitude": 19.5, "timeZones": [{"zoneName": "Europe/Bratislava", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "SI", "isoCode3": "SVN", "numericCode": "705", "name": "Slovenia", "phonecode": "386", "flag": "🇸🇮", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 46.11666666, "longitude": 14.81666666, "timeZones": [{"zoneName": "Europe/Ljubljana", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "SB", "isoCode3": "SLB", "numericCode": "090", "name": "Solomon Islands", "phonecode": "677", "flag": "🇸🇧", "currency": "SBD", "currencySymbol": "Si$", "currencyName": "Solomon Islands dollar", "latitude": -8, "longitude": 159, "timeZones": [{"zoneName": "Pacific/Guadalcanal", "gmtOffset": 39600, "gmtOffsetName": "UTC+11:00", "abbreviation": "SBT", "tzName": "Solomon Islands Time"}]}, {"isoCode2": "SO", "isoCode3": "SOM", "numericCode": "706", "name": "Somalia", "phonecode": "252", "flag": "🇸🇴", "currency": "SOS", "currencySymbol": "Sh.so.", "currencyName": "Somali shilling", "latitude": 10, "longitude": 49, "timeZones": [{"zoneName": "Africa/Mogadishu", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "EAT", "tzName": "East Africa Time"}]}, {"isoCode2": "ZA", "isoCode3": "ZAF", "numericCode": "710", "name": "South Africa", "phonecode": "27", "flag": "🇿🇦", "currency": "ZAR", "currencySymbol": "R", "currencyName": "South African rand", "latitude": -29, "longitude": 24, "timeZones": [{"zoneName": "Africa/Johannesburg", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "SAST", "tzName": "South African Standard Time"}]}, {"isoCode2": "GS", "isoCode3": "SGS", "numericCode": "239", "name": "South Georgia", "phonecode": "500", "flag": "🇬🇸", "currency": "GBP", "currencySymbol": "£", "currencyName": "British pound", "latitude": -54.5, "longitude": -37, "timeZones": [{"zoneName": "Atlantic/South_Georgia", "gmtOffset": -7200, "gmtOffsetName": "UTC-02:00", "abbreviation": "GST", "tzName": "South Georgia and the South Sandwich Islands Time"}]}, {"isoCode2": "KR", "isoCode3": "KOR", "numericCode": "410", "name": "South Korea", "phonecode": "82", "flag": "🇰🇷", "currency": "KRW", "currencySymbol": "₩", "currencyName": "Won", "latitude": 37, "longitude": 127.5, "timeZones": [{"zoneName": "Asia/Seoul", "gmtOffset": 32400, "gmtOffsetName": "UTC+09:00", "abbreviation": "KST", "tzName": "Korea Standard Time"}]}, {"isoCode2": "SS", "isoCode3": "SSD", "numericCode": "728", "name": "South Sudan", "phonecode": "211", "flag": "🇸🇸", "currency": "SSP", "currencySymbol": "£", "currencyName": "South Sudanese pound", "latitude": 7, "longitude": 30, "timeZones": [{"zoneName": "Africa/Juba", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "EAT", "tzName": "East Africa Time"}]}, {"isoCode2": "ES", "isoCode3": "ESP", "numericCode": "724", "name": "Spain", "phonecode": "34", "flag": "🇪🇸", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 40, "longitude": -4, "timeZones": [{"zoneName": "Africa/Ceuta", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}, {"zoneName": "Atlantic/Canary", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "WET", "tzName": "Western European Time"}, {"zoneName": "Europe/Madrid", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "LK", "isoCode3": "LKA", "numericCode": "144", "name": "Sri Lanka", "phonecode": "94", "flag": "🇱🇰", "currency": "LKR", "currencySymbol": "Rs", "currencyName": "Sri Lankan rupee", "latitude": 7, "longitude": 81, "timeZones": [{"zoneName": "Asia/Colombo", "gmtOffset": 19800, "gmtOffsetName": "UTC+05:30", "abbreviation": "IST", "tzName": "Indian Standard Time"}]}, {"isoCode2": "SD", "isoCode3": "SDN", "numericCode": "729", "name": "Sudan", "phonecode": "249", "flag": "🇸🇩", "currency": "SDG", "currencySymbol": ".س.ج", "currencyName": "Sudanese pound", "latitude": 15, "longitude": 30, "timeZones": [{"zoneName": "Africa/Khartoum", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EAT", "tzName": "Eastern African Time"}]}, {"isoCode2": "SR", "isoCode3": "SUR", "numericCode": "740", "name": "Suriname", "phonecode": "597", "flag": "🇸🇷", "currency": "SRD", "currencySymbol": "$", "currencyName": "Surinamese dollar", "latitude": 4, "longitude": -56, "timeZones": [{"zoneName": "America/Paramaribo", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "SRT", "tzName": "Suriname Time"}]}, {"isoCode2": "SJ", "isoCode3": "SJM", "numericCode": "744", "name": "Svalbard and Jan Mayen Islands", "phonecode": "47", "flag": "🇸🇯", "currency": "NOK", "currencySymbol": "ko", "currencyName": "Norwegian krone", "latitude": 78, "longitude": 20, "timeZones": [{"zoneName": "Arctic/Longyearbyen", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "SE", "isoCode3": "SWE", "numericCode": "752", "name": "Sweden", "phonecode": "46", "flag": "🇸🇪", "currency": "SEK", "currencySymbol": "ko", "currencyName": "Swedish krona", "latitude": 62, "longitude": 15, "timeZones": [{"zoneName": "Europe/Stockholm", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "CH", "isoCode3": "CHE", "numericCode": "756", "name": "Switzerland", "phonecode": "41", "flag": "🇨🇭", "currency": "CHF", "currencySymbol": "CHf", "currencyName": "Swiss franc", "latitude": 47, "longitude": 8, "timeZones": [{"zoneName": "Europe/Zurich", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "SY", "isoCode3": "SYR", "numericCode": "760", "name": "Syria", "phonecode": "963", "flag": "🇸🇾", "currency": "SYP", "currencySymbol": "LS", "currencyName": "Syrian pound", "latitude": 35, "longitude": 38, "timeZones": [{"zoneName": "Asia/Damascus", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "TW", "isoCode3": "TWN", "numericCode": "158", "name": "Taiwan", "phonecode": "886", "flag": "🇹🇼", "currency": "TWD", "currencySymbol": "$", "currencyName": "New Taiwan dollar", "latitude": 23.5, "longitude": 121, "timeZones": [{"zoneName": "Asia/Taipei", "gmtOffset": 28800, "gmtOffsetName": "UTC+08:00", "abbreviation": "CST", "tzName": "China Standard Time"}]}, {"isoCode2": "TJ", "isoCode3": "TJK", "numericCode": "762", "name": "Tajikistan", "phonecode": "992", "flag": "🇹🇯", "currency": "TJS", "currencySymbol": "SM", "currencyName": "<PERSON>i somoni", "latitude": 39, "longitude": 71, "timeZones": [{"zoneName": "Asia/Dushanbe", "gmtOffset": 18000, "gmtOffsetName": "UTC+05:00", "abbreviation": "TJT", "tzName": "Tajikistan Time"}]}, {"isoCode2": "TZ", "isoCode3": "TZA", "numericCode": "834", "name": "Tanzania", "phonecode": "255", "flag": "🇹🇿", "currency": "TZS", "currencySymbol": "TSh", "currencyName": "Tanzanian shilling", "latitude": -6, "longitude": 35, "timeZones": [{"zoneName": "Africa/Dar_es_Salaam", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "EAT", "tzName": "East Africa Time"}]}, {"isoCode2": "TH", "isoCode3": "THA", "numericCode": "764", "name": "Thailand", "phonecode": "66", "flag": "🇹🇭", "currency": "THB", "currencySymbol": "฿", "currencyName": "Thai baht", "latitude": 15, "longitude": 100, "timeZones": [{"zoneName": "Asia/Bangkok", "gmtOffset": 25200, "gmtOffsetName": "UTC+07:00", "abbreviation": "ICT", "tzName": "Indochina Time"}]}, {"isoCode2": "BS", "isoCode3": "BHS", "numericCode": "044", "name": "The Bahamas", "phonecode": "1", "flag": "🇧🇸", "currency": "BSD", "currencySymbol": "B$", "currencyName": "Bahamian dollar", "latitude": 24.25, "longitude": -76, "timeZones": [{"zoneName": "America/Nassau", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America)"}]}, {"isoCode2": "GM", "isoCode3": "GMB", "numericCode": "270", "name": "The Gambia ", "phonecode": "220", "flag": "🇬🇲", "currency": "GMD", "currencySymbol": "D", "currencyName": "Gambian dalasi", "latitude": 13.46666666, "longitude": -16.56666666, "timeZones": [{"zoneName": "Africa/Banjul", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "TL", "isoCode3": "TLS", "numericCode": "626", "name": "Timor-Leste", "phonecode": "670", "flag": "🇹🇱", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": -8.83333333, "longitude": 125.91666666, "timeZones": [{"zoneName": "Asia/Dili", "gmtOffset": 32400, "gmtOffsetName": "UTC+09:00", "abbreviation": "TLT", "tzName": "Timor Leste Time"}]}, {"isoCode2": "TG", "isoCode3": "TGO", "numericCode": "768", "name": "Togo", "phonecode": "228", "flag": "🇹🇬", "currency": "XOF", "currencySymbol": "CFA", "currencyName": "West African CFA franc", "latitude": 8, "longitude": 1.16666666, "timeZones": [{"zoneName": "Africa/Lome", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "TK", "isoCode3": "TKL", "numericCode": "772", "name": "Tokelau", "phonecode": "690", "flag": "🇹🇰", "currency": "NZD", "currencySymbol": "$", "currencyName": "New Zealand dollar", "latitude": -9, "longitude": -172, "timeZones": [{"zoneName": "Pacific/Fakaofo", "gmtOffset": 46800, "gmtOffsetName": "UTC+13:00", "abbreviation": "TKT", "tzName": "Tokelau Time"}]}, {"isoCode2": "TO", "isoCode3": "TON", "numericCode": "776", "name": "Tonga", "phonecode": "676", "flag": "🇹🇴", "currency": "TOP", "currencySymbol": "$", "currencyName": "Tongan paʻanga", "latitude": -20, "longitude": -175, "timeZones": [{"zoneName": "Pacific/Tongatapu", "gmtOffset": 46800, "gmtOffsetName": "UTC+13:00", "abbreviation": "TOT", "tzName": "Tonga Time"}]}, {"isoCode2": "TT", "isoCode3": "TTO", "numericCode": "780", "name": "Trinidad and Tobago", "phonecode": "1", "flag": "🇹🇹", "currency": "TTD", "currencySymbol": "$", "currencyName": "Trinidad and Tobago dollar", "latitude": 11, "longitude": -61, "timeZones": [{"zoneName": "America/Port_of_Spain", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "TN", "isoCode3": "TUN", "numericCode": "788", "name": "Tunisia", "phonecode": "216", "flag": "🇹🇳", "currency": "TND", "currencySymbol": "ت.د", "currencyName": "Tunisian dinar", "latitude": 34, "longitude": 9, "timeZones": [{"zoneName": "Africa/Tunis", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "TR", "isoCode3": "TUR", "numericCode": "792", "name": "Turkey", "phonecode": "90", "flag": "🇹🇷", "currency": "TRY", "currencySymbol": "₺", "currencyName": "Turkish lira", "latitude": 39, "longitude": 35, "timeZones": [{"zoneName": "Europe/Istanbul", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "TM", "isoCode3": "TKM", "numericCode": "795", "name": "Turkmenistan", "phonecode": "993", "flag": "🇹🇲", "currency": "TMT", "currencySymbol": "T", "currencyName": "Turkmenistan manat", "latitude": 40, "longitude": 60, "timeZones": [{"zoneName": "Asia/Ashgabat", "gmtOffset": 18000, "gmtOffsetName": "UTC+05:00", "abbreviation": "TMT", "tzName": "Turkmenistan Time"}]}, {"isoCode2": "TC", "isoCode3": "TCA", "numericCode": "796", "name": "Turks and Caicos Islands", "phonecode": "1", "flag": "🇹🇨", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": 21.75, "longitude": -71.58333333, "timeZones": [{"zoneName": "America/Grand_Turk", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}]}, {"isoCode2": "TV", "isoCode3": "TUV", "numericCode": "798", "name": "Tuvalu", "phonecode": "688", "flag": "🇹🇻", "currency": "AUD", "currencySymbol": "$", "currencyName": "Australian dollar", "latitude": -8, "longitude": 178, "timeZones": [{"zoneName": "Pacific/Funafuti", "gmtOffset": 43200, "gmtOffsetName": "UTC+12:00", "abbreviation": "TVT", "tzName": "Tuvalu Time"}]}, {"isoCode2": "UG", "isoCode3": "UGA", "numericCode": "800", "name": "Uganda", "phonecode": "256", "flag": "🇺🇬", "currency": "UGX", "currencySymbol": "USh", "currencyName": "Ugandan shilling", "latitude": 1, "longitude": 32, "timeZones": [{"zoneName": "Africa/Kampala", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "EAT", "tzName": "East Africa Time"}]}, {"isoCode2": "UA", "isoCode3": "UKR", "numericCode": "804", "name": "Ukraine", "phonecode": "380", "flag": "🇺🇦", "currency": "UAH", "currencySymbol": "₴", "currencyName": "Ukrainian hryvnia", "latitude": 49, "longitude": 32, "timeZones": [{"zoneName": "Europe/Kiev", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}, {"zoneName": "Europe/Simferopol", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "MSK", "tzName": "Moscow Time"}, {"zoneName": "Europe/Uzhgorod", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}, {"zoneName": "Europe/Zaporozhye", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "EET", "tzName": "Eastern European Time"}]}, {"isoCode2": "AE", "isoCode3": "ARE", "numericCode": "784", "name": "United Arab Emirates", "phonecode": "971", "flag": "🇦🇪", "currency": "AED", "currencySymbol": "<PERSON><PERSON>د", "currencyName": "United Arab Emirates dirham", "latitude": 24, "longitude": 54, "timeZones": [{"zoneName": "Asia/Dubai", "gmtOffset": 14400, "gmtOffsetName": "UTC+04:00", "abbreviation": "GST", "tzName": "Gulf Standard Time"}]}, {"isoCode2": "GB", "isoCode3": "GBR", "numericCode": "826", "name": "United Kingdom", "phonecode": "44", "flag": "🇬🇧", "currency": "GBP", "currencySymbol": "£", "currencyName": "British pound", "latitude": 54, "longitude": -2, "timeZones": [{"zoneName": "Europe/London", "gmtOffset": 0, "gmtOffsetName": "UTC±00", "abbreviation": "GMT", "tzName": "Greenwich Mean Time"}]}, {"isoCode2": "US", "isoCode3": "USA", "numericCode": "840", "name": "United States", "phonecode": "1", "flag": "🇺🇸", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": 38, "longitude": -97, "timeZones": [{"zoneName": "America/Adak", "gmtOffset": -36000, "gmtOffsetName": "UTC-10:00", "abbreviation": "HST", "tzName": "Hawaii–Aleutian Standard Time"}, {"zoneName": "America/Anchorage", "gmtOffset": -32400, "gmtOffsetName": "UTC-09:00", "abbreviation": "AKST", "tzName": "Alaska Standard Time"}, {"zoneName": "America/Boise", "gmtOffset": -25200, "gmtOffsetName": "UTC-07:00", "abbreviation": "MST", "tzName": "Mountain Standard Time (North America"}, {"zoneName": "America/Chicago", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/Denver", "gmtOffset": -25200, "gmtOffsetName": "UTC-07:00", "abbreviation": "MST", "tzName": "Mountain Standard Time (North America"}, {"zoneName": "America/Detroit", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}, {"zoneName": "America/Indiana/Indianapolis", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}, {"zoneName": "America/Indiana/Knox", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/Indiana/Marengo", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}, {"zoneName": "America/Indiana/Petersburg", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}, {"zoneName": "America/Indiana/Tell_City", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/Indiana/Vevay", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}, {"zoneName": "America/Indiana/Vincennes", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}, {"zoneName": "America/Indiana/Winamac", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}, {"zoneName": "America/Juneau", "gmtOffset": -32400, "gmtOffsetName": "UTC-09:00", "abbreviation": "AKST", "tzName": "Alaska Standard Time"}, {"zoneName": "America/Kentucky/Louisville", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}, {"zoneName": "America/Kentucky/Monticello", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}, {"zoneName": "America/Los_Angeles", "gmtOffset": -28800, "gmtOffsetName": "UTC-08:00", "abbreviation": "PST", "tzName": "Pacific Standard Time (North America"}, {"zoneName": "America/Menominee", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/Metlakatla", "gmtOffset": -32400, "gmtOffsetName": "UTC-09:00", "abbreviation": "AKST", "tzName": "Alaska Standard Time"}, {"zoneName": "America/New_York", "gmtOffset": -18000, "gmtOffsetName": "UTC-05:00", "abbreviation": "EST", "tzName": "Eastern Standard Time (North America"}, {"zoneName": "America/Nome", "gmtOffset": -32400, "gmtOffsetName": "UTC-09:00", "abbreviation": "AKST", "tzName": "Alaska Standard Time"}, {"zoneName": "America/North_Dakota/Beulah", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/North_Dakota/Center", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/North_Dakota/New_Salem", "gmtOffset": -21600, "gmtOffsetName": "UTC-06:00", "abbreviation": "CST", "tzName": "Central Standard Time (North America"}, {"zoneName": "America/Phoenix", "gmtOffset": -25200, "gmtOffsetName": "UTC-07:00", "abbreviation": "MST", "tzName": "Mountain Standard Time (North America"}, {"zoneName": "America/Sitka", "gmtOffset": -32400, "gmtOffsetName": "UTC-09:00", "abbreviation": "AKST", "tzName": "Alaska Standard Time"}, {"zoneName": "America/Yakutat", "gmtOffset": -32400, "gmtOffsetName": "UTC-09:00", "abbreviation": "AKST", "tzName": "Alaska Standard Time"}, {"zoneName": "Pacific/Honolulu", "gmtOffset": -36000, "gmtOffsetName": "UTC-10:00", "abbreviation": "HST", "tzName": "Hawaii–Aleutian Standard Time"}]}, {"isoCode2": "UM", "isoCode3": "UMI", "numericCode": "581", "name": "United States Minor Outlying Islands", "phonecode": "1", "flag": "🇺🇲", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": 0, "longitude": 0, "timeZones": [{"zoneName": "Pacific/Midway", "gmtOffset": -39600, "gmtOffsetName": "UTC-11:00", "abbreviation": "SST", "tzName": "Samoa Standard Time"}, {"zoneName": "Pacific/Wake", "gmtOffset": 43200, "gmtOffsetName": "UTC+12:00", "abbreviation": "WAKT", "tzName": "Wake Island Time"}]}, {"isoCode2": "UY", "isoCode3": "URY", "numericCode": "858", "name": "Uruguay", "phonecode": "598", "flag": "🇺🇾", "currency": "UYU", "currencySymbol": "$", "currencyName": "Uruguayan peso", "latitude": -33, "longitude": -56, "timeZones": [{"zoneName": "America/Montevideo", "gmtOffset": -10800, "gmtOffsetName": "UTC-03:00", "abbreviation": "UYT", "tzName": "Uruguay Standard Time"}]}, {"isoCode2": "UZ", "isoCode3": "UZB", "numericCode": "860", "name": "Uzbekistan", "phonecode": "998", "flag": "🇺🇿", "currency": "UZS", "currencySymbol": "лв", "currencyName": "Uzbekistani soʻm", "latitude": 41, "longitude": 64, "timeZones": [{"zoneName": "Asia/Samarkand", "gmtOffset": 18000, "gmtOffsetName": "UTC+05:00", "abbreviation": "UZT", "tzName": "Uzbekistan Time"}, {"zoneName": "Asia/Tashkent", "gmtOffset": 18000, "gmtOffsetName": "UTC+05:00", "abbreviation": "UZT", "tzName": "Uzbekistan Time"}]}, {"isoCode2": "VU", "isoCode3": "VUT", "numericCode": "548", "name": "Vanuatu", "phonecode": "678", "flag": "🇻🇺", "currency": "VUV", "currencySymbol": "VT", "currencyName": "Vanuatu vatu", "latitude": -16, "longitude": 167, "timeZones": [{"zoneName": "Pacific/Efate", "gmtOffset": 39600, "gmtOffsetName": "UTC+11:00", "abbreviation": "VUT", "tzName": "Vanuatu Time"}]}, {"isoCode2": "VA", "isoCode3": "VAT", "numericCode": "336", "name": "Vatican City State (Holy See)", "phonecode": "379", "flag": "🇻🇦", "currency": "EUR", "currencySymbol": "€", "currencyName": "Euro", "latitude": 41.9, "longitude": 12.45, "timeZones": [{"zoneName": "Europe/Vatican", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "CET", "tzName": "Central European Time"}]}, {"isoCode2": "VE", "isoCode3": "VEN", "numericCode": "862", "name": "Venezuela", "phonecode": "58", "flag": "🇻🇪", "currency": "VES", "currencySymbol": "Bs", "currencyName": "Bolívar", "latitude": 8, "longitude": -66, "timeZones": [{"zoneName": "America/Caracas", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "VET", "tzName": "Venezuelan Standard Time"}]}, {"isoCode2": "VN", "isoCode3": "VNM", "numericCode": "704", "name": "Vietnam", "phonecode": "84", "flag": "🇻🇳", "currency": "VND", "currencySymbol": "₫", "currencyName": "Vietnamese đồng", "latitude": 16.16666666, "longitude": 107.83333333, "timeZones": [{"zoneName": "Asia/Ho_Chi_Minh", "gmtOffset": 25200, "gmtOffsetName": "UTC+07:00", "abbreviation": "ICT", "tzName": "Indochina Time"}]}, {"isoCode2": "VG", "isoCode3": "VGB", "numericCode": "092", "name": "Virgin Islands (British)", "phonecode": "1", "flag": "🇻🇬", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": 18.431383, "longitude": -64.62305, "timeZones": [{"zoneName": "America/Tortola", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "VI", "isoCode3": "VIR", "numericCode": "850", "name": "Virgin Islands (US)", "phonecode": "1", "flag": "🇻🇮", "currency": "USD", "currencySymbol": "$", "currencyName": "United States dollar", "latitude": 18.34, "longitude": -64.93, "timeZones": [{"zoneName": "America/St_Thomas", "gmtOffset": -14400, "gmtOffsetName": "UTC-04:00", "abbreviation": "AST", "tzName": "Atlantic Standard Time"}]}, {"isoCode2": "WF", "isoCode3": "WLF", "numericCode": "876", "name": "Wallis and Futuna Islands", "phonecode": "681", "flag": "🇼🇫", "currency": "XPF", "currencySymbol": "₣", "currencyName": "CFP franc", "latitude": -13.3, "longitude": -176.2, "timeZones": [{"zoneName": "Pacific/Wallis", "gmtOffset": 43200, "gmtOffsetName": "UTC+12:00", "abbreviation": "WFT", "tzName": "Wallis & Futuna Time"}]}, {"isoCode2": "EH", "isoCode3": "ESH", "numericCode": "732", "name": "Western Sahara", "phonecode": "212", "flag": "🇪🇭", "currency": "MAD", "currencySymbol": "MAD", "currencyName": "Moroccan dirham", "latitude": 24.5, "longitude": -13, "timeZones": [{"zoneName": "Africa/El_Aaiun", "gmtOffset": 3600, "gmtOffsetName": "UTC+01:00", "abbreviation": "WEST", "tzName": "Western European Summer Time"}]}, {"isoCode2": "YE", "isoCode3": "YEM", "numericCode": "887", "name": "Yemen", "phonecode": "967", "flag": "🇾🇪", "currency": "YER", "currencySymbol": "﷼", "currencyName": "Yemeni rial", "latitude": 15, "longitude": 48, "timeZones": [{"zoneName": "Asia/Aden", "gmtOffset": 10800, "gmtOffsetName": "UTC+03:00", "abbreviation": "AST", "tzName": "Arabia Standard Time"}]}, {"isoCode2": "ZM", "isoCode3": "ZMB", "numericCode": "894", "name": "Zambia", "phonecode": "260", "flag": "🇿🇲", "currency": "ZMW", "currencySymbol": "ZK", "currencyName": "Zambian kwacha", "latitude": -15, "longitude": 30, "timeZones": [{"zoneName": "Africa/Lusaka", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "CAT", "tzName": "Central Africa Time"}]}, {"isoCode2": "ZW", "isoCode3": "ZWE", "numericCode": "716", "name": "Zimbabwe", "phonecode": "263", "flag": "🇿🇼", "currency": "ZWL", "currencySymbol": "$", "currencyName": "Zimbabwe Dollar", "latitude": -20, "longitude": 30, "timeZones": [{"zoneName": "Africa/Harare", "gmtOffset": 7200, "gmtOffsetName": "UTC+02:00", "abbreviation": "CAT", "tzName": "Central Africa Time"}]}]