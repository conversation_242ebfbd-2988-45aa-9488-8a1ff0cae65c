const fs = require('fs');

const filePath = 'C:/Data/users/Sonu Singla/Spectriv/Ritefit.AI/New Backend/prisma/data/countries.json'; // Your JSON file

// Step 1: Read original data
const rawData = JSON.parse(fs.readFileSync(filePath, 'utf8'));

// Step 2: Map each object to match Prisma schema
const transformed = rawData.map((country) => ({
  isoCode2: country.iso2,
  isoCode3: country.iso3,
  numericCode: country.numeric_code,
  name: country.name,
  phonecode: country.phonecode,
  flag: country.emoji || null,
  currency: country.currency || null,
  currencySymbol: country.currency_symbol || null,
  currencyName: country.currency_name || null,
  latitude: parseFloat(country.latitude),
  longitude: parseFloat(country.longitude),
  timeZones: country.timezones,
}));

// Step 3: Overwrite the same file
fs.writeFileSync(filePath, JSON.stringify(transformed, null, 2));

console.log('✔️ countries.json updated with transformed data.');
