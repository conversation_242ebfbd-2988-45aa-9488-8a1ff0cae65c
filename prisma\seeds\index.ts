import { PrismaClient } from '@prisma/client';
import seedCountries from './countries';
import seedStates from './states';
import seedCities from './cities';

const prisma = new PrismaClient();

async function main() {
  await seedCountries();
  await seedStates();
  await seedCities();
}

main()
  .then(() => console.log('🌱 Seeding complete'))
  .catch((e) => {
    console.error(e);
    process.exit(1);
})
.finally(() => prisma.$disconnect());
