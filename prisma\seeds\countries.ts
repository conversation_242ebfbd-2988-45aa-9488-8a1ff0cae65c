import { PrismaClient } from '@prisma/client';
import {logger} from '../../src/utils/logger';
import config from '../../src/config/config';
import countriesData from '../data/countries.json';

const prisma = new PrismaClient();

export const seedCountries = async () => {

  for (const country of countriesData) {
    await prisma.countries.create({
      data: {
        name: country.name,
        isoCode2: country.isoCode2, // optional: only if available
        isoCode3: country.isoCode3,
        numericCode: country.numericCode,
        phonecode: country.phonecode,
        currency: country.currency,
        currencySymbol: country.currencySymbol,
        currencyName: country.currencyName,
        latitude: country.latitude,
        longitude: country.longitude,
        timeZones: country.timeZones, // Prisma @db.Json
        createdBy: config.SYSTEM_USER_ID ,
        updatedBy: config.SYSTEM_USER_ID,
        status: 'active',
      },
    });
  }

  logger.info('Seeding countries...');
};

export default seedCountries;
